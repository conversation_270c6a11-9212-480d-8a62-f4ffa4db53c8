<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Bad Robot AGI System</title>
    <meta name="description" content="Bad Robot AGI System - Advanced Artificial General Intelligence Platform">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">
    
    <style>
        :root {
            --primary: #2563eb;
            --secondary: #64748b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --robot-blue: #1e40af;
            --robot-green: #059669;
            --robot-purple: #7c3aed;
            --bg-color: #f8fafc;
            --text-color: #1e293b;
            --card-color: #ffffff;
            --border-color: #e2e8f0;
            --shadow: 0 4px 12px rgba(0,0,0,0.1);
            --border-radius: 12px;
            --robot-gradient: linear-gradient(135deg, var(--robot-blue), var(--robot-purple));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            max-width: 800px;
            padding: 2rem;
            text-align: center;
        }

        .header {
            background: var(--robot-gradient);
            color: white;
            padding: 3rem 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .option-card {
            background: var(--card-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .option-card h3 {
            color: var(--robot-blue);
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .option-card p {
            color: var(--secondary);
            margin-bottom: 1.5rem;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--robot-gradient);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(37, 99, 235, 0.4);
        }

        .status {
            background: var(--card-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--shadow);
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .status-item:last-child {
            border-bottom: none;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success);
        }

        .status-indicator.warning {
            background: var(--warning);
        }

        .status-indicator.error {
            background: var(--danger);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Bad Robot AGI System</h1>
            <p>Advanced Artificial General Intelligence Platform</p>
        </div>

        <div class="options">
            <div class="option-card" onclick="window.location.href='../unified_index.html'">
                <h3>🌐 Full System Interface</h3>
                <p>Access the complete Bad Robot AGI system with all features including multi-modal capabilities, AI chat, vision processing, and system management.</p>
                <a href="../unified_index.html" class="btn">Launch Full Interface</a>
            </div>

            <div class="option-card" onclick="window.location.href='index_chat (1).html'">
                <h3>💬 Chat Interface</h3>
                <p>Dedicated chat interface with voice recognition, web search, and advanced AI conversation capabilities.</p>
                <a href="index_chat (1).html" class="btn">Open Chat</a>
            </div>

            <div class="option-card" onclick="window.location.href='index_full.html'">
                <h3>🔧 Advanced Interface</h3>
                <p>Complete system interface with all advanced features, games, file management, and comprehensive system controls.</p>
                <a href="index_full.html" class="btn">Advanced Mode</a>
            </div>
        </div>

        <div class="status">
            <h3 style="margin-bottom: 1rem; color: var(--robot-blue);">🔍 System Status</h3>
            <div class="status-item">
                <span>Flask Server</span>
                <div class="status-indicator" id="flask-status"></div>
            </div>
            <div class="status-item">
                <span>Communication Hub</span>
                <div class="status-indicator warning" id="hub-status"></div>
            </div>
            <div class="status-item">
                <span>Agent System</span>
                <div class="status-indicator" id="agent-status"></div>
            </div>
            <div class="status-item">
                <span>Database</span>
                <div class="status-indicator" id="db-status"></div>
            </div>
        </div>
    </div>

    <script>
        // System status check
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();

            // Update status every 30 seconds
            setInterval(checkSystemStatus, 30000);
        });

        async function checkSystemStatus() {
            // Check Flask server (current server)
            try {
                const response = await fetch('/status');
                document.getElementById('flask-status').className = 'status-indicator';
            } catch (error) {
                document.getElementById('flask-status').className = 'status-indicator warning';
            }

            // Check Communication Hub
            try {
                const response = await fetch('http://localhost:8080/status');
                document.getElementById('hub-status').className = 'status-indicator';
            } catch (error) {
                document.getElementById('hub-status').className = 'status-indicator warning';
            }

            // Check Agent System (via main system)
            try {
                const response = await fetch('/api/agents');
                document.getElementById('agent-status').className = 'status-indicator';
            } catch (error) {
                document.getElementById('agent-status').className = 'status-indicator warning';
            }

            // Database status (always available for SQLite)
            document.getElementById('db-status').className = 'status-indicator';
        }

        // Add click handlers for cards
        document.querySelectorAll('.option-card').forEach(card => {
            card.addEventListener('click', function() {
                const link = this.querySelector('a');
                if (link) {
                    window.location.href = link.href;
                }
            });
        });
    </script>
</body>
</html>
