<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Bad Robot AGI System - Complete Interface</title>
    <meta name="description" content="Complete Bad Robot AGI System with all features: multi-modal capabilities, real-time AI chat, vision processing, face recognition, games, shop, file management, and comprehensive system management">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🤖</text></svg>">

    <!-- Enhanced Libraries for Bad Robot AGI -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/js-base64@3.7.7/base64.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs@latest"></script>
    <script src="https://cdn.jsdelivr.net/pyodide/v0.25.0/full/pyodide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@1.6.0/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/universal-sentence-encoder@1.3.3/dist/universal-sentence-encoder.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/toxicity@1.2.2/dist/toxicity.min.js"></script>

    <style>
        /* === BAD ROBOT AGI SYSTEM STYLES === */
        :root {
            /* 🤖 Enhanced Bad Robot Color Scheme */
            --primary: #2563eb;
            --secondary: #64748b;
            --success: #10b981;
            --danger: #ef4444;
            --warning: #f59e0b;
            --info: #06b6d4;
            --robot-blue: #1e40af;
            --robot-green: #059669;
            --robot-purple: #7c3aed;
            --robot-orange: #ea580c;
            --robot-cyan: #0891b2;
            --robot-pink: #ec4899;
            --face-recognition: #ff6b6b;
            --face-detected: #4ecdc4;
            --face-authenticated: #45b7d1;
            --bg-color: #f8fafc;
            --text-color: #1e293b;
            --card-color: #ffffff;
            --border-color: #e2e8f0;
            --shadow: 0 4px 12px rgba(0,0,0,0.1);
            --border-radius: 12px;
            --robot-gradient: linear-gradient(135deg, var(--robot-blue), var(--robot-purple));
            --robot-gradient-alt: linear-gradient(135deg, var(--robot-green), var(--robot-cyan));
            --robot-gradient-warm: linear-gradient(135deg, var(--robot-orange), var(--robot-pink));
            --animation-speed: 0.3s;
        }

        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #0f172a;
                --text-color: #f1f5f9;
                --card-color: #1e293b;
                --border-color: #334155;
                --robot-gradient: linear-gradient(135deg, #1e40af, #7c3aed);
            }
        }

        /* === Base Styles === */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 15px;
            background-color: var(--bg-color);
            color: var(--text-color);
            display: grid;
            gap: 15px;
            grid-template-areas:
                "header header header header"
                "vision chat face-rec sidebar"
                "vision audio face-rec sidebar"
                "files models agents python"
                "games games shop shop"
                "graphs graphs graphs events";
            grid-template-columns: 1.2fr 1.2fr 1fr 1fr;
            grid-template-rows: auto auto auto auto auto auto;
            gap: 15px;
            min-height: 100vh;
            box-sizing: border-box;
        }

        h1, h2, h3, h4 {
            color: var(--primary);
            margin-top: 0;
        }

        /* Panel Styles */
        .panel {
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header-panel { grid-area: header; }
        .vision-panel { grid-area: vision; }
        .chat-panel { grid-area: chat; }
        .face-rec-panel { grid-area: face-rec; }
        .sidebar-panel { grid-area: sidebar; gap: 15px; padding: 0; background: none; box-shadow: none;}
        .audio-panel { grid-area: audio; }
        .files-panel { grid-area: files; }
        .models-panel { grid-area: models; }
        .agents-panel { grid-area: agents; }
        .python-panel { grid-area: python; }
        .shop-panel { grid-area: shop; }
        .games-panel { 
            grid-area: games;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
        }
        .graphs-panel { 
            grid-area: graphs; 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 15px; 
        }
        .events-panel { grid-area: events; }

        /* === Component Styles === */

        /* 🤖 Panel Headers with Robot Styling */
        .panel h2 {
            margin: 0 0 15px 0;
            background: var(--robot-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.2em;
            font-weight: 700;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 8px;
            position: relative;
        }

        .panel h2::before {
            content: "🤖";
            position: absolute;
            left: -25px;
            top: 0;
            font-size: 0.9em;
        }

        /* 🔧 Robot-themed Buttons */
        button {
            background: var(--robot-gradient);
            color: white;
            border: none;
            padding: 12px 18px;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 3px;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: "";
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background: var(--secondary);
            cursor: not-allowed;
            transform: none;
            opacity: 0.6;
        }

        button.success { background: linear-gradient(135deg, var(--success), var(--robot-green)); }
        button.danger { background: linear-gradient(135deg, var(--danger), #dc2626); }
        button.warning { background: linear-gradient(135deg, var(--warning), var(--robot-orange)); }
        button.info { background: linear-gradient(135deg, var(--info), #0891b2); }

        /* Input Fields */
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--card-color);
            color: var(--text-color);
            font-size: 14px;
            margin: 5px 0;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        /* Camera Styles */
        .camera-container {
            position: relative;
            width: 100%;
            padding-top: 75%;
            margin-bottom: 15px;
            border-radius: 8px;
            overflow: hidden;
            background-color: #000;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        #camera-feed, #face-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1);
        }

        #detection-overlay, #face-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .detection-box {
            position: absolute;
            border: 2px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
            pointer-events: none;
        }

        .detection-label {
            position: absolute;
            bottom: 0;
            left: 0;
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 2px 4px;
            font-size: 11px;
        }

        /* Chat Styles */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: var(--bg-color);
            margin-bottom: 10px;
            max-height: 300px;
        }

        .message {
            margin-bottom: 10px;
            padding: 8px 12px;
            border-radius: var(--border-radius);
            max-width: 80%;
        }

        .message.user {
            background-color: var(--primary);
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .message.agi, .message.assistant {
            background-color: var(--success);
            color: white;
        }

        .message.system {
            background-color: var(--info);
            color: white;
        }

        .message.error {
            background-color: var(--danger);
            color: white;
        }

        .message.face-recognition {
            background-color: var(--face-recognition);
            color: white;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .chat-input-container {
            display: flex;
            gap: 5px;
        }

        .chat-input-container input {
            flex: 1;
            margin: 0;
        }

        .chat-input-container button {
            margin: 0;
            white-space: nowrap;
        }

        /* Controls */
        .controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .controls button {
            flex: 1;
            min-width: 120px;
        }

        /* Detection Results */
        .detection-results {
            background-color: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }

        /* System Status */
        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 10px;
            background-color: var(--bg-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .status-item .label {
            font-weight: bold;
        }

        .status-item .value {
            color: var(--primary);
        }

        /* File Upload */
        .file-drop-zone {
            border: 2px dashed var(--border-color);
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s, border-color 0.3s;
            font-size: 0.9em;
        }

        .file-drop-zone.dragover {
            background-color: rgba(0, 123, 255, 0.1);
            border-color: var(--primary);
        }

        .file-list {
            max-height: 120px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.85em;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px dashed var(--border-color);
        }

        .file-item:last-child {
            border-bottom: none;
        }

        /* Models & Agents */
        .model-selector-container, .agent-selector-container {
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 15px;
        }

        .model-stats {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
            font-size: 0.85em;
        }

        #model-status {
            color: var(--secondary);
        }

        #model-memory {
            width: 100%;
        }

        /* Shop & Avatar */
        .shop-panel {
            grid-area: shop;
        }

        .buy-button {
            background: var(--success);
            width: 100%;
            margin-top: 8px;
        }

        #player-avatar {
            width: 80px;
            height: 80px;
            background: #eee;
            border-radius: 50%;
            margin: 10px auto;
            border: 3px solid var(--primary);
            display: block;
        }

        .shop-items {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .shop-item {
            background: var(--bg-color);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            flex: 1 1 120px;
        }

        .shop-item img {
            max-width: 80px;
            height: auto;
            border-radius: 4px;
            display: block;
            margin: 0 auto 5px auto;
        }

        .item-details {
            font-size: 0.85em;
        }

        .item-details span {
            display: block;
            color: var(--secondary);
            font-size: 0.9em;
        }

        /* Games */
        .game-panel {
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            flex-direction: column;
            min-height: 400px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .game-panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: var(--primary);
        }

        .dqn-game-header {
            background: var(--info);
            color: white;
            padding: 10px 15px;
            border-radius: 8px 8px 0 0;
            margin: -20px -20px 15px -20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dqn-game-header h3 {
            color: white;
            margin: 0;
        }

        .dqn-game-header select {
            background-color: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.5);
        }

        canvas.game-canvas {
            width: 100%;
            height: 180px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            border-radius: 8px;
            margin-bottom: 15px;
            border: 2px solid var(--primary);
            box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);
            transition: border-color 0.3s ease;
        }

        canvas.game-canvas:hover {
            border-color: var(--success);
        }

        .dqn-game-controls {
            display: flex;
            flex-direction: row;
            gap: 8px;
            margin-top: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .dqn-game-controls button {
            flex: 1;
            min-width: 80px;
            padding: 8px 12px;
            border: none;
            border-radius: 6px;
            font-weight: bold;
            font-size: 0.85em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .dqn-game-controls button.success {
            background: var(--success);
            color: white;
        }

        .dqn-game-controls button.danger {
            background: var(--danger);
            color: white;
        }

        .dqn-game-controls button.warning {
            background: var(--warning);
            color: white;
        }

        .dqn-game-controls button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .dqn-game-stats {
            background: rgba(0,0,0,0.1);
            padding: 10px;
            border-radius: 8px;
            font-size: 0.8em;
            line-height: 1.4;
            margin-top: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dqn-game-stats p {
            margin: 4px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dqn-game-stats span {
            font-weight: bold;
            color: var(--primary);
            background: rgba(255, 255, 255, 0.1);
            padding: 2px 6px;
            border-radius: 4px;
        }

        .game-stats {
            background: rgba(0,0,0,0.05);
            padding: 8px;
            border-radius: 5px;
            font-size: 0.85em;
            line-height: 1.5;
            margin-top:5px;
        }

        .game-stats p {
            margin: 2px 0;
        }

        /* Graphs */
        .graphs-panel h3 {
            grid-column: 1 / -1;
        }

        .graph-container {
            background-color: var(--card-color);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .graph-container canvas {
            max-height: 200px;
        }

        /* Events */
        .events-panel ul {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 150px;
            overflow-y: auto;
        }

        .events-panel li {
            padding: 6px 0;
            border-bottom: 1px solid var(--border-color);
            font-size: 0.85em;
        }

        .events-panel li:last-child {
            border-bottom: none;
        }

        /* Notifications */
        #notification-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            width: 300px;
        }

        .notification {
            padding: 12px 18px;
            border-radius: 8px;
            box-shadow: 0 3px 8px rgba(0,0,0,0.2);
            margin-bottom: 10px;
            opacity: 1;
            transition: opacity 0.5s ease;
            font-size: 0.9em;
        }

        .notification.success {
            background: var(--success);
            color: white;
        }

        .notification.error {
            background: var(--danger);
            color: white;
        }

        .notification.info {
            background: var(--info);
            color: white;
        }

        .notification.warning {
            background: var(--warning);
            color: black;
        }

        .notification.fade-out {
            animation: fadeOut 3s forwards;
        }

        @keyframes fadeOut {
            0% { opacity: 1; }
            80% { opacity: 1; }
            100% { opacity: 0; transform: translateY(-20px); }
        }

        /* Face Recognition Specific Styles */
        .face-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .face-stat-item {
            background: var(--bg-color);
            padding: 10px;
            border-radius: var(--border-radius);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .face-stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: var(--face-detected);
        }

        .face-stat-label {
            font-size: 0.8em;
            color: var(--secondary);
            margin-top: 5px;
        }

        .user-info {
            background: var(--face-authenticated);
            color: white;
            padding: 10px;
            border-radius: var(--border-radius);
            margin-top: 10px;
        }

        .user-info h4 {
            margin: 0 0 5px 0;
            color: white;
        }

        /* Audio Controls */
        .audio-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
            margin-top: 10px;
        }

        #audioPreview {
            flex-grow: 1;
            min-width: 150px;
        }

        #volume-slider {
            max-width: 100px;
        }

        #audioVisualizer {
            width: 100%;
            height: 50px;
            background-color: #333;
            border-radius: 4px;
            margin-bottom: 10px;
        }

        #mic-button.recording {
            animation: pulse 1.5s infinite;
            background-color: var(--danger) !important;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
            100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
        }

        /* Python Execution Panel */
        .python-execution {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .code-editor {
            display: flex;
            flex-direction: column;
        }

        .code-editor textarea {
            font-family: 'Courier New', monospace;
            height: 200px;
            resize: vertical;
        }

        .execution-output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: var(--border-radius);
            font-family: 'Courier New', monospace;
            min-height: 150px;
            overflow-y: auto;
            border: 2px solid var(--border-color);
            margin-top: 15px;
        }

        /* Responsive Adjustments */
        @media (max-width: 1400px) {
            body {
                grid-template-areas:
                   "header header header"
                   "vision chat face-rec"
                   "vision audio sidebar"
                   "files models agents"
                   "python python python"
                   "games games shop"
                   "graphs graphs graphs"
                   "events events events";
                grid-template-columns: 1fr 1fr 1fr;
            }
        }

        @media (max-width: 1200px) {
            body {
                grid-template-areas:
                   "header header"
                   "vision chat"
                   "face-rec audio"
                   "sidebar sidebar"
                   "files models"
                   "agents python"
                   "shop shop"
                   "games games"
                   "graphs graphs"
                   "events events";
                grid-template-columns: 1fr 1fr;
            }
            .sidebar-panel {
                flex-direction: row;
                flex-wrap: wrap;
            }
            .model-selector-container, .agent-selector-container {
                flex: 1 1 200px;
            }
        }

        @media (max-width: 768px) {
            body {
                grid-template-areas:
                   "header"
                   "vision"
                   "chat"
                   "face-rec"
                   "audio"
                   "sidebar"
                   "files"
                   "models"
                   "agents"
                   "python"
                   "shop"
                   "games"
                   "graphs"
                   "events";
                grid-template-columns: 1fr;
            }
            .games-panel {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 15px;
            }
            .game-panel {
                min-height: 350px;
            }
            canvas.game-canvas {
                height: 150px;
            }
            .dqn-game-controls {
                flex-direction: column;
                gap: 6px;
            }
            .dqn-game-controls button {
                min-width: 100%;
            }
            .graphs-panel {
                grid-template-columns: 1fr;
            }
            .sidebar-panel {
                flex-direction: column;
            }
            .python-execution {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 🤖 Enhanced Bad Robot Header Panel -->
    <div class="panel header-panel" style="background: var(--robot-gradient); color: white; text-align: center; position: relative; overflow: hidden;">
        <h1 style="margin: 0; font-size: 2.8em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); position: relative; z-index: 2;">
            🤖 BAD ROBOT AGI SYSTEM - COMPLETE INTERFACE 🛠️
        </h1>
        <p style="margin: 10px 0; font-size: 1.3em; opacity: 0.95; position: relative; z-index: 2;">
            🧠 Complete Multi-Modal Artificial General Intelligence Platform ⚙️
        </p>

        <!-- Enhanced System Status Dashboard -->
        <div style="background: rgba(255,255,255,0.15); border-radius: 12px; padding: 20px; margin-top: 20px; backdrop-filter: blur(10px); position: relative; z-index: 2;">
            <div class="system-status" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 12px;">
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">🔗 Connection:</span>
                    <span class="value status-indicator" id="connection-status">Connecting...</span>
                </div>
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">🤖 Agents:</span>
                    <span class="value" id="agents-count">0</span>
                </div>
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">⚙️ Modules:</span>
                    <span class="value" id="modules-count">0</span>
                </div>
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">🔌 Hub:</span>
                    <span class="value status-indicator" id="hub-status">Disconnected</span>
                </div>
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">⏱️ Uptime:</span>
                    <span class="value" id="system-uptime">00:00:00</span>
                </div>
                <div class="status-item enhanced-status" style="background: rgba(255,255,255,0.2); border-radius: 8px; padding: 12px;">
                    <span class="label">🧠 AI Mode:</span>
                    <span class="value" id="ai-mode">Intelligent</span>
                </div>
            </div>

            <!-- Server Navigation -->
            <div style="margin-top: 15px; display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                <button onclick="window.open('http://localhost:8000', '_blank')" class="nav-button" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                    🏠 Main (8000)
                </button>
                <button onclick="window.open('http://localhost:8001', '_blank')" class="nav-button" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                    ⚡ Enhanced (8001)
                </button>
                <button onclick="window.open('http://localhost:8002', '_blank')" class="nav-button" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                    🎮 Gaming (8002)
                </button>
                <button onclick="window.open('http://localhost:9000', '_blank')" class="nav-button" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 16px; border-radius: 6px; cursor: pointer; transition: all 0.3s;">
                    📡 Hub (9000)
                </button>
            </div>
        </div>
    </div>

    <!-- 👁️ Vision Panel -->
    <div class="panel vision-panel">
        <h2>👁️ Visión Robótica</h2>
        <div class="camera-container">
            <video id="camera-feed" autoplay playsinline muted></video>
            <canvas id="detection-overlay"></canvas>
            <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                🤖 ROBOT VISION
            </div>
        </div>
        <div class="controls">
            <button id="toggle-camera" class="success">📹 Iniciar Cámara</button>
            <button id="toggle-yolo-mode" class="info">🎯 Modo: Default</button>
            <input type="file" id="image-upload-input" accept="image/*" style="display: none;">
            <button id="upload-image-btn" class="warning">📁 Subir Imagen</button>
            <button id="start-face-detection" class="info">👤 Detección Facial</button>
            <button id="stop-face-detection" class="danger">⏹️ Detener</button>
        </div>
        <div class="detection-results" id="detection-results" style="background: var(--bg-color); border: 2px dashed var(--border-color); padding: 15px; border-radius: var(--border-radius);">
            🤖 Esperando detecciones robóticas...
        </div>
    </div>

    <!-- 💬 Chat Panel -->
    <div class="panel chat-panel">
        <h2>💬 Chat con Robot AGI</h2>
        <div class="chat-container">
            <div class="chat-messages" id="chat-messages">
                <div class="message system">
                    <div class="message-header">
                        <span class="sender">🤖 Bad Robot System</span>
                        <span class="timestamp"></span>
                    </div>
                    <div class="message-content">
                        🚀 Sistema AGI Bad Robot inicializado correctamente.<br>
                        🛠️ ¡Hola! Soy tu asistente robótico completo. ¿En qué puedo ayudarte?<br>
                        💡 Prueba comandos como: <code>status</code>, <code>agents</code>, <code>help</code>, <code>search:</code>
                    </div>
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" id="chat-input" placeholder="🤖 Escribe tu mensaje para el robot..." autocomplete="off">
                <button id="send-button" title="Enviar Mensaje" class="success">📤 Enviar</button>
                <button id="mic-button" title="Grabar Voz" class="info">🎤 Voz</button>
            </div>
        </div>
        <div class="controls" style="margin-top: 10px;">
            <label for="llm-mode-select" style="font-size: 0.9em;">🧠 Modelo:</label>
            <select id="llm-mode-select">
                <option value="local">🏠 Local (GGUF/Mamba)</option>
                <option value="openai" selected>🌐 OpenAI</option>
                <option value="claude">🎭 Claude (Anthropic)</option>
                <option value="huggingface">🤗 HuggingFace</option>
                <option value="neurosymbolic">🧠 NeuroSymbolic</option>
                <option value="custom_ai">🤖 Bad Robot AI</option>
            </select>
        </div>
        <div class="controls">
            <label for="response-mode-select" style="font-size: 0.9em;">🎯 Respuesta:</label>
            <select id="response-mode-select">
                <option value="text">📝 Solo Texto</option>
                <option value="audio">🔊 Solo Voz</option>
                <option value="both" selected>🔄 Texto + Voz</option>
            </select>
            <button id="web-search-toggle" class="info">🌐 Web Search</button>
        </div>
    </div>

    <!-- 👤 Face Recognition Panel -->
    <div class="panel face-rec-panel">
        <h2>👤 Reconocimiento Facial</h2>
        <div class="camera-container">
            <video id="face-video" autoplay playsinline muted></video>
            <canvas id="face-overlay"></canvas>
            <div style="position: absolute; top: 10px; right: 10px; background: rgba(0,0,0,0.7); color: white; padding: 5px 10px; border-radius: 15px; font-size: 12px;">
                👁️ FACE RECOGNITION
            </div>
        </div>
        <div class="controls">
            <button id="start-face-recognition" class="success">👁️ Iniciar Reconocimiento</button>
            <button id="stop-face-recognition" class="danger">⏹️ Detener</button>
            <button id="register-face" class="warning">📝 Registrar Cara</button>
            <button id="clear-faces" class="info">🗑️ Limpiar Datos</button>
        </div>
        <div class="face-stats">
            <div class="face-stat-item">
                <div class="face-stat-value" id="faces-detected">0</div>
                <div class="face-stat-label">Caras Detectadas</div>
            </div>
            <div class="face-stat-item">
                <div class="face-stat-value" id="unique-users">0</div>
                <div class="face-stat-label">Usuarios Únicos</div>
            </div>
            <div class="face-stat-item">
                <div class="face-stat-value" id="recognition-accuracy">0%</div>
                <div class="face-stat-label">Precisión</div>
            </div>
            <div class="face-stat-item">
                <div class="face-stat-value" id="emotion-confidence">0%</div>
                <div class="face-stat-label">Confianza Emoción</div>
            </div>
        </div>
        <div id="current-user-info" class="user-info" style="display: none;">
            <h4>Usuario Actual</h4>
            <p><strong>Nombre:</strong> <span id="user-name">-</span></p>
            <p><strong>Rol:</strong> <span id="user-role">-</span></p>
            <p><strong>Interacciones:</strong> <span id="user-interactions">0</span></p>
            <p><strong>Última vez:</strong> <span id="user-last-seen">-</span></p>
        </div>
    </div>

    <!-- 🔧 Sidebar Panel -->
    <div class="sidebar-panel">
        <!-- System Controls -->
        <div class="panel">
            <h2>🎛️ Controles Robóticos</h2>
            <div class="controls">
                <button id="system-status-button" class="info">📊 Estado del Sistema</button>
                <button id="refresh-button" class="warning">🔄 Actualizar</button>
            </div>
            <div id="system-status" style="margin-top: 10px; padding: 10px; background: var(--bg-color); border-radius: var(--border-radius); font-family: monospace; font-size: 12px;"></div>
        </div>

        <!-- Agent Controls -->
        <div class="panel">
            <h2>🤖 Control de Agentes</h2>
            <select id="agent-select" style="margin-bottom: 10px;">
                <option value="">🤖 Seleccionar Agente Robot...</option>
                <option value="financial_agent">💰 Agente Financiero</option>
                <option value="medical_agent">🏥 Agente Médico</option>
                <option value="law_agent">⚖️ Agente Legal</option>
                <option value="dqn_agent">🧠 Agente DQN</option>
                <option value="quantum_developer_agent">⚛️ Agente Cuántico</option>
                <option value="apk_manipulation_agent">📱 Agente APK</option>
            </select>
            <input type="text" id="command-input" placeholder="🛠️ Comando para el agente robot..." style="margin-bottom: 10px;">
            <button id="execute-button" class="success">⚡ Ejecutar Comando</button>
        </div>

        <!-- Robot Status -->
        <div class="panel">
            <h2>🔋 Estado del Robot</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 12px;">
                <div style="text-align: center; padding: 10px; background: var(--bg-color); border-radius: var(--border-radius);">
                    <div style="font-size: 20px;">🔋</div>
                    <div>Energía: <span id="robot-energy">100%</span></div>
                </div>
                <div style="text-align: center; padding: 10px; background: var(--bg-color); border-radius: var(--border-radius);">
                    <div style="font-size: 20px;">🧠</div>
                    <div>CPU: <span id="robot-cpu">45%</span></div>
                </div>
                <div style="text-align: center; padding: 10px; background: var(--bg-color); border-radius: var(--border-radius);">
                    <div style="font-size: 20px;">💾</div>
                    <div>RAM: <span id="robot-ram">67%</span></div>
                </div>
                <div style="text-align: center; padding: 10px; background: var(--bg-color); border-radius: var(--border-radius);">
                    <div style="font-size: 20px;">🌐</div>
                    <div>Red: <span id="robot-network">Online</span></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 🔊 Audio Panel -->
    <div class="panel audio-panel">
        <h2>🔊 Audio Robótico</h2>
        <div class="audio-controls">
            <canvas id="audioVisualizer"></canvas>
        </div>
        <div class="controls">
            <label for="voice-select">🗣️ Voz:</label>
            <select id="voice-select">
                <option value="es-ES">🇪🇸 Español</option>
                <option value="en-US">🇺🇸 English</option>
                <option value="fr-FR">🇫🇷 Français</option>
            </select>
        </div>
        <div class="controls">
            <label for="volume-slider">🔊 Volumen:</label>
            <input type="range" id="volume-slider" min="0" max="100" value="50">
            <span id="volume-display">50%</span>
        </div>
        <audio id="audio-player" controls style="width: 100%; margin-top: 10px; display: none; border-radius: var(--border-radius);"></audio>
        <div style="text-align: center; margin-top: 10px; font-size: 12px; color: var(--secondary);">
            🎤 Robot listo para comunicación de voz
        </div>
    </div>

    <!-- 📁 Files Panel -->
    <div class="panel files-panel">
        <h2>📁 Gestión de Archivos</h2>
        <div class="file-drop-zone" id="fileDropZone">
            <div style="font-size: 2em; margin-bottom: 10px;">📁</div>
            <p>Arrastra archivos aquí o haz clic para seleccionar</p>
            <p style="font-size: 0.9em; opacity: 0.7;">Soporta: Imágenes, Documentos, Código, Modelos</p>
            <input type="file" id="fileInput" multiple accept="*/*" style="display: none;">
        </div>
        <div id="fileList" style="margin-top: 15px;">
            <h4>📋 Archivos Subidos</h4>
            <div id="fileItems" style="max-height: 200px; overflow-y: auto;">
                <p style="opacity: 0.7; text-align: center;">No hay archivos subidos</p>
            </div>
        </div>
        <div class="controls">
            <button id="clear-files" class="warning">🗑️ Limpiar Archivos</button>
            <button id="download-files" class="info">💾 Descargar Todo</button>
        </div>
    </div>

    <!-- 🧠 Models Panel -->
    <div class="panel models-panel">
        <h2>🧠 Modelos IA</h2>
        <select id="gguf-models">
            <option value="">Cargando modelos...</option>
        </select>
        <div class="model-stats" style="margin: 10px 0;">
            <progress id="model-memory" value="0" max="100" title="Uso de memoria estimado"></progress>
            <span id="model-status">Inactivo</span>
        </div>
        <div class="controls">
            <button id="load-model-btn" class="success">🚀 Cargar Modelo</button>
            <button id="unload-model-btn" class="danger">🗑️ Descargar</button>
        </div>
        <div style="margin-top: 10px; font-size: 0.85em;">
            <p><strong>Modelo Actual:</strong> <span id="current-model-name">Ninguno</span></p>
            <p><strong>Tamaño:</strong> <span id="current-model-size">0 GB</span></p>
            <p><strong>Velocidad:</strong> <span id="model-speed">0 tokens/s</span></p>
        </div>
    </div>

    <!-- 🤖 Agents Panel -->
    <div class="panel agents-panel">
        <h2>🤖 Agentes Especializados</h2>
        <div class="controls">
            <select id="agent-strategy-select">
                <option value="balanced">⚖️ Equilibrado</option>
                <option value="aggressive">⚔️ Agresivo</option>
                <option value="defensive">🛡️ Defensivo</option>
                <option value="creative">🎨 Creativo</option>
                <option value="analytical">🔬 Analítico</option>
            </select>
            <button id="vote-agent" class="success">🗳️ Votar Estrategia</button>
        </div>
        <div class="game-stats">
            <p>Agente Actual: <span id="current-agent">Equilibrado</span></p>
            <p>Nivel IA Global: <span id="global-ai-level">1</span></p>
            <p>Experiencia: <span id="agent-experience">0 XP</span></p>
            <p>Eficiencia: <span id="agent-efficiency">85%</span></p>
        </div>
        <div class="controls">
            <button id="train-agent" class="warning">🏋️ Entrenar Agente</button>
            <button id="reset-agent" class="danger">🔄 Reiniciar</button>
        </div>
    </div>

    <!-- 🐍 Python & AI Execution Panel -->
    <div class="panel python-panel">
        <h2>🐍 Python & AI Execution</h2>
        <div class="python-execution">
            <!-- Python Code Editor -->
            <div class="code-editor">
                <h3>🐍 Python Code (Pyodide)</h3>
                <textarea id="python-code" placeholder="# 🐍 Escribe código Python aquí
import numpy as np
import matplotlib.pyplot as plt

# Ejemplo: Crear datos
x = np.linspace(0, 10, 100)
y = np.sin(x)

print('🤖 Bad Robot Python ejecutándose!')
print(f'Datos generados: {len(x)} puntos')
"></textarea>
                <div style="margin-top: 10px;">
                    <button id="run-python" class="success">🚀 Ejecutar Python</button>
                    <button id="clear-python" class="warning">🧹 Limpiar</button>
                </div>
            </div>

            <!-- TensorFlow.js Model -->
            <div class="code-editor">
                <h3>🧠 TensorFlow.js Model</h3>
                <textarea id="tfjs-code" placeholder="// 🧠 Código TensorFlow.js
// Crear un modelo simple
const model = tf.sequential({
  layers: [
    tf.layers.dense({inputShape: [1], units: 1})
  ]
});

// Compilar el modelo
model.compile({optimizer: 'sgd', loss: 'meanSquaredError'});

// Datos de entrenamiento
const xs = tf.tensor2d([1, 2, 3, 4], [4, 1]);
const ys = tf.tensor2d([1, 3, 5, 7], [4, 1]);

console.log('🤖 Modelo TensorFlow.js creado!');
"></textarea>
                <div style="margin-top: 10px;">
                    <button id="run-tfjs" class="info">🧠 Ejecutar TF.js</button>
                    <button id="clear-tfjs" class="warning">🧹 Limpiar</button>
                </div>
            </div>
        </div>

        <!-- Execution Output -->
        <div style="margin-top: 15px;">
            <h3>📊 Salida de Ejecución</h3>
            <div id="execution-output" class="execution-output">
                🤖 Bad Robot AI Execution Console<br>
                🐍 Pyodide Status: <span id="pyodide-status">Loading...</span><br>
                🧠 TensorFlow.js Status: <span id="tfjs-status">Loading...</span><br>
                ⚡ Ready for AI execution!<br>
            </div>
        </div>
    </div>

    <!-- 🛒 Shop Panel -->
    <div class="panel shop-panel">
        <h2>🛒 Tienda Virtual</h2>
        <div style="text-align: center; margin-bottom: 15px;">
            <img id="player-avatar" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><circle cx='50' cy='50' r='40' fill='%234CAF50'/><text x='50' y='60' text-anchor='middle' font-size='30'>🤖</text></svg>" alt="Avatar">
            <p><strong>Monedas:</strong> <span id="coin-count">100</span> 🪙</p>
        </div>
        <div class="shop-items" id="shop-items">
            <div class="shop-item">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%23FF5722'/><text x='50' y='60' text-anchor='middle' font-size='30'>🎩</text></svg>" alt="Hat">
                <div class="item-details">
                    <strong>Sombrero Elegante</strong>
                    <span>Precio: 50 🪙</span>
                    <button class="buy-button" data-item-id="hat" data-price="50" data-name="Sombrero">Comprar</button>
                </div>
            </div>
            <div class="shop-item">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%232196F3'/><text x='50' y='60' text-anchor='middle' font-size='30'>👓</text></svg>" alt="Glasses">
                <div class="item-details">
                    <strong>Gafas Inteligentes</strong>
                    <span>Precio: 75 🪙</span>
                    <button class="buy-button" data-item-id="glasses" data-price="75" data-name="Gafas">Comprar</button>
                </div>
            </div>
            <div class="shop-item">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><rect width='100' height='100' fill='%234CAF50'/><text x='50' y='60' text-anchor='middle' font-size='30'>⚡</text></svg>" alt="Power">
                <div class="item-details">
                    <strong>Boost de Energía</strong>
                    <span>Precio: 25 🪙</span>
                    <button class="buy-button" data-item-id="energy" data-price="25" data-name="Energía">Comprar</button>
                </div>
            </div>
        </div>
        <div class="controls">
            <button id="earn-coins" class="success">💰 Ganar Monedas</button>
            <button id="view-inventory" class="info">🎒 Ver Inventario</button>
        </div>
    </div>

    <!-- 🎮 Games Panel -->
    <div class="panel games-panel">
        <h2>🎮 Juegos y Simulaciones</h2>

        <!-- DQN Game 1 -->
        <div class="game-panel">
            <div class="dqn-game-header">
                <h3>🤖 Agente DQN Inteligente</h3>
                <select id="game-mode-select">
                    <option value="TRAINING">🧠 Entrenamiento</option>
                    <option value="PLAYING">🎮 Juego</option>
                    <option value="DEMO">👁️ Demo</option>
                </select>
            </div>
            <canvas id="game-canvas" class="game-canvas" width="380" height="180"></canvas>
            <div class="dqn-game-controls">
                <button id="start-game" class="success">🎮 Iniciar Juego</button>
                <button id="stop-game" class="danger">⏹️ Detener Juego</button>
                <button id="reset-game" class="warning">🔄 Reiniciar</button>
            </div>
            <div class="dqn-game-stats">
                <p>Estado: <span id="game-status">Detenido</span></p>
                <p>Puntuación: <span id="score">0</span></p>
                <p>Nivel: <span id="level">1</span></p>
                <p>Agente IA: <span id="ai-status">Inactivo</span></p>
            </div>
        </div>

        <!-- DQN Game 2 -->
        <div class="game-panel">
            <div class="dqn-game-header">
                <h3>🎯 Juego Multi-Agente</h3>
                <select id="game-mode-select2">
                    <option value="SURVIVAL">🛡️ Supervivencia</option>
                    <option value="CAPTURE_THE_FLAG">🚩 Captura la Bandera</option>
                    <option value="POINT_RACE">🏁 Carrera de Puntos</option>
                </select>
            </div>
            <canvas id="dqn-game-canvas2" class="game-canvas" width="380" height="180"></canvas>
            <div class="dqn-game-controls">
                <button id="start-game2" class="success">🎮 Iniciar</button>
                <button id="stop-game2" class="danger">⏹️ Detener</button>
                <button id="reset-game2" class="warning">🔄 Reiniciar</button>
            </div>
            <div class="dqn-game-stats">
                <p>Puntuación: <span id="score2">0</span></p>
                <p>Tiempo/Meta: <span id="time2">N/A</span></p>
                <p>Agentes: <span id="agent-count2">1</span></p>
            </div>
        </div>
    </div>

    <!-- 📊 Graphs Panel -->
    <div class="panel graphs-panel">
        <h2>📊 Métricas y Gráficos</h2>
        <div class="graph-container">
            <h4>📈 Rendimiento del Sistema</h4>
            <canvas id="performance-chart"></canvas>
        </div>
        <div class="graph-container">
            <h4>🧠 Actividad Cognitiva</h4>
            <canvas id="network-chart"></canvas>
        </div>
        <div class="graph-container">
            <h4>🎮 Progreso de Entrenamiento</h4>
            <canvas id="training-chart"></canvas>
        </div>
    </div>

    <!-- 📋 Events Panel -->
    <div class="panel events-panel">
        <h2>📋 Eventos del Sistema</h2>
        <ul id="events-list">
            <li>🚀 Sistema Bad Robot AGI inicializado</li>
            <li>🤖 Módulos de IA cargados</li>
            <li>📡 Conexión establecida</li>
        </ul>
        <div class="controls">
            <button id="clear-events" class="warning">🗑️ Limpiar Eventos</button>
            <button id="export-events" class="info">💾 Exportar Log</button>
        </div>
    </div>

    <!-- 🔔 Notification Container -->
    <div id="notification-container"></div>

    <!-- 🤖 Bad Robot AGI Client Application Scripts -->
    <!-- External scripts commented out for standalone operation -->
    <!-- <script src="/static/js/camera-system.js"></script> -->
    <!-- <script src="/static/js/face-detection.js"></script> -->
    <!-- <script src="/static/js/game.js"></script> -->
    <!-- <script src="/static/js/shop-system.js"></script> -->
    <!-- <script src="/static/js/agi-client.js"></script> -->

    <script>
        // 🚀 Initialize Bad Robot AGI Complete System
        let pyodide = null;
        let tfReady = false;
        let socket = null;
        let startTime = Date.now();
        let webSearchEnabled = false;
        let currentAIProvider = 'bad_robot';
        let faceRecognitionEnabled = true;
        let emotionAnalysisEnabled = true;
        let storeFaceDataEnabled = false;

        // 🧠 Professional Neural-Symbolic DQN Agent Variables
        let neuralSymbolicAgent = null;
        let knowledgeBase = null;
        let tensorflowReady = false;

        // Face Recognition Variables
        let videoElement;
        let overlayCanvas;
        let overlayContext;
        let stream;
        let faceDetectionInterval;
        let isModelLoaded = false;
        let detectedFaces = [];
        let knownFaces = [];
        let currentUser = null;
        let faceRecognitionStats = {
            totalDetected: 0,
            uniqueUsers: 0,
            accuracy: 0,
            emotionConfidence: 0
        };

        // Vision System Variables
        let cameraStream = null;
        let visionActive = false;
        let currentCameraMode = 'default';
        let yoloTFJSModel = null;

        // Audio System Variables
        let speechRecognition = null;
        let audioContext = null;
        let audioAnalyser = null;
        let audioVisualizerDataArray = null;
        let audioVisualizerBufferLength = null;

        // Game System Variables
        let gameAnimations = {};
        let playerCoins = 100;
        let playerInventory = new Set();

        // File Management Variables
        let uploadedFiles = [];
        let currentModel = null;

        // 🤖 Initialize Socket.IO Connection
        function initializeSocket() {
            try {
                socket = io('http://localhost:5000');

                socket.on('connect', () => {
                    document.getElementById('connection-status').textContent = 'Connected ✅';
                    showNotification('🔌 Connected to Bad Robot Server', 'success');
                    console.log('Socket.IO connected to server.');
                });

                socket.on('disconnect', () => {
                    document.getElementById('connection-status').textContent = 'Disconnected ❌';
                    showNotification('⚠️ Disconnected from server', 'warning');
                });

                socket.on('agi_response', (data) => {
                    addMessage('agi', data.text, new Date().toISOString());
                });

                socket.on('system_status', (data) => {
                    addMessage('system', `[System] ${data.message}`, new Date().toISOString());
                });

            } catch (error) {
                console.error('Error initializing Socket.IO:', error);
                showNotification('❌ Socket.IO connection error', 'error');
            }
        }

        // 💬 Chat Functions
        function addMessage(sender, text, timestamp) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const senderName = sender === 'user' ? '👤 User' :
                              sender === 'agi' || sender === 'assistant' ? '🤖 Bad Robot AGI' :
                              '🔧 System';

            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="sender">${senderName}</span>
                    <span class="timestamp">${new Date(timestamp).toLocaleTimeString()}</span>
                </div>
                <div class="message-content">${text}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendMessage() {
            const chatInput = document.getElementById('chat-input');
            const message = chatInput.value.trim();

            if (!message) return;

            addMessage('user', message, new Date().toISOString());
            chatInput.value = '';

            // Process message
            processMessage(message);
        }

        async function processMessage(message) {
            const lowerMessage = message.toLowerCase();

            try {
                let response = '';

                // Handle special commands
                if (lowerMessage === 'help') {
                    response = generateHelpResponse();
                } else if (lowerMessage === 'status') {
                    response = generateStatusResponse();
                } else if (lowerMessage.startsWith('search:')) {
                    const searchQuery = message.substring(7).trim();
                    response = await performWebSearch(searchQuery);
                } else {
                    // Generate AI response
                    if (socket && socket.connected) {
                        socket.emit('text_message', {
                            text: message,
                            model: currentAIProvider
                        });
                        return; // Response will come via socket
                    } else {
                        response = await generateLocalResponse(message);
                    }
                }

                if (response) {
                    addMessage('assistant', response, new Date().toISOString());
                }

            } catch (error) {
                console.error('Error processing message:', error);
                addMessage('system', '❌ Error processing message', new Date().toISOString());
            }
        }

        function generateHelpResponse() {
            return `🤖 **Bad Robot AGI Complete System Help:**

**Available Commands:**
• \`help\` - Show this help
• \`status\` - System status
• \`search: [query]\` - Web search
• \`agents\` - List available agents
• \`models\` - Show AI models

**Features:**
• 👁️ **Vision System**: Real-time object detection
• 👤 **Face Recognition**: Advanced facial analysis
• 🎮 **Games**: DQN training simulations
• 🛒 **Virtual Shop**: Avatar customization
• 📁 **File Management**: Upload and process files
• 🧠 **AI Models**: Multiple AI backends
• 🐍 **Python Execution**: Run Python code in browser
• 📊 **Analytics**: Real-time system metrics

**Examples:**
• "search: latest AI research"
• "What can you do?"
• "Show me the system status"
• "Help me with programming"`;
        }

        function generateStatusResponse() {
            return `🤖 **Bad Robot AGI System Status:**

**Core Systems:**
• 🔌 Socket.IO: ${socket && socket.connected ? '✅ Connected' : '❌ Disconnected'}
• 👁️ Vision System: ${visionActive ? '✅ Active' : '❌ Inactive'}
• 👤 Face Recognition: ${faceRecognitionEnabled ? '✅ Enabled' : '❌ Disabled'}
• 🧠 TensorFlow.js: ${tfReady ? '✅ Ready' : '❌ Loading'}
• 🐍 Pyodide: ${pyodide ? '✅ Ready' : '❌ Loading'}

**Statistics:**
• 📁 Files Uploaded: ${uploadedFiles.length}
• 🪙 Player Coins: ${playerCoins}
• 🎮 Games Active: ${Object.keys(gameAnimations).length}
• ⏱️ Uptime: ${formatUptime(Date.now() - startTime)}

**AI Provider:** ${currentAIProvider}
**Web Search:** ${webSearchEnabled ? '✅ Enabled' : '❌ Disabled'}`;
        }

        async function generateLocalResponse(message) {
            const responses = [
                `🤖 I understand you're asking about "${message}". Let me process that for you.`,
                `💭 Interesting question about "${message}". Here's my analysis...`,
                `🧠 Processing your request: "${message}". Based on my knowledge...`,
                `🔍 I've analyzed "${message}" and here's what I can tell you...`,
                `⚡ Your message about "${message}" is very relevant. Let me help...`
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
        }

        // 🔔 Notification System
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            container.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    container.removeChild(notification);
                }, 3000);
            }, 3000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('🤖 Bad Robot AGI Complete System Loading...');

            // Initialize all systems
            initializeSocket();
            initializeChat();
            initializeVisionSystem();
            initializeFaceRecognition();
            initializeAudioSystem();
            initializeFileSystem();
            initializeShopSystem();
            initializeGameSystems();
            initializePythonExecution();
            initializeCharts();
            updateSystemStatus();

            // Start system uptime counter
            setInterval(updateSystemUptime, 1000);

            showNotification('🤖 Bad Robot AGI Complete System Initialized!', 'success');
        });

        // 💬 Initialize Chat System
        function initializeChat() {
            const chatInput = document.getElementById('chat-input');
            const sendButton = document.getElementById('send-button');
            const micButton = document.getElementById('mic-button');

            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });

            sendButton.addEventListener('click', sendMessage);
            micButton.addEventListener('click', toggleVoiceRecognition);

            // Initialize speech recognition if available
            if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                speechRecognition = new SpeechRecognition();
                speechRecognition.continuous = false;
                speechRecognition.interimResults = false;
                speechRecognition.lang = 'es-ES';

                speechRecognition.onresult = function(event) {
                    const transcript = event.results[0][0].transcript.trim();
                    chatInput.value = transcript;
                    sendMessage();
                };

                speechRecognition.onerror = function(event) {
                    console.error('Speech recognition error:', event.error);
                    showNotification('Error in speech recognition', 'error');
                };

                speechRecognition.onend = function() {
                    micButton.classList.remove('recording');
                    micButton.textContent = '🎤 Voz';
                };
            }
        }

        function toggleVoiceRecognition() {
            if (!speechRecognition) {
                showNotification('Speech recognition not supported', 'warning');
                return;
            }

            const micButton = document.getElementById('mic-button');

            if (micButton.classList.contains('recording')) {
                speechRecognition.stop();
                micButton.classList.remove('recording');
                micButton.textContent = '🎤 Voz';
            } else {
                speechRecognition.start();
                micButton.classList.add('recording');
                micButton.textContent = '🔴 Recording...';
            }
        }

        // 👁️ Initialize Vision System
        function initializeVisionSystem() {
            const toggleCameraBtn = document.getElementById('toggle-camera');
            const uploadImageBtn = document.getElementById('upload-image-btn');
            const imageUploadInput = document.getElementById('image-upload-input');

            toggleCameraBtn.addEventListener('click', toggleCamera);
            uploadImageBtn.addEventListener('click', () => imageUploadInput.click());
            imageUploadInput.addEventListener('change', handleImageUpload);
        }

        async function toggleCamera() {
            const video = document.getElementById('camera-feed');
            const toggleBtn = document.getElementById('toggle-camera');

            if (!visionActive) {
                try {
                    cameraStream = await navigator.mediaDevices.getUserMedia({
                        video: { width: 640, height: 480 }
                    });
                    video.srcObject = cameraStream;
                    visionActive = true;
                    toggleBtn.textContent = '⏹️ Detener Cámara';
                    toggleBtn.className = 'danger';
                    showNotification('📹 Camera activated', 'success');
                } catch (error) {
                    console.error('Error accessing camera:', error);
                    showNotification('❌ Camera access denied', 'error');
                }
            } else {
                if (cameraStream) {
                    cameraStream.getTracks().forEach(track => track.stop());
                    video.srcObject = null;
                    cameraStream = null;
                }
                visionActive = false;
                toggleBtn.textContent = '📹 Iniciar Cámara';
                toggleBtn.className = 'success';
                showNotification('📹 Camera deactivated', 'info');
            }
        }

        function handleImageUpload(event) {
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        // Process uploaded image
                        showNotification(`📁 Image uploaded: ${file.name}`, 'success');
                        addMessage('system', `📁 Image uploaded and processed: ${file.name}`, new Date().toISOString());
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }

        // 👤 Initialize Face Recognition
        function initializeFaceRecognition() {
            const startBtn = document.getElementById('start-face-recognition');
            const stopBtn = document.getElementById('stop-face-recognition');
            const registerBtn = document.getElementById('register-face');
            const clearBtn = document.getElementById('clear-faces');

            startBtn.addEventListener('click', startFaceRecognition);
            stopBtn.addEventListener('click', stopFaceRecognition);
            registerBtn.addEventListener('click', registerFace);
            clearBtn.addEventListener('click', clearFaceData);
        }

        async function startFaceRecognition() {
            const video = document.getElementById('face-video');

            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480 }
                });
                video.srcObject = stream;
                faceRecognitionEnabled = true;
                showNotification('👁️ Face recognition started', 'success');
                addMessage('system', '👁️ Face recognition system activated', new Date().toISOString());
            } catch (error) {
                console.error('Error starting face recognition:', error);
                showNotification('❌ Face recognition failed to start', 'error');
            }
        }

        function stopFaceRecognition() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                document.getElementById('face-video').srcObject = null;
                stream = null;
            }
            faceRecognitionEnabled = false;
            showNotification('👁️ Face recognition stopped', 'info');
        }

        function registerFace() {
            if (!faceRecognitionEnabled) {
                showNotification('⚠️ Start face recognition first', 'warning');
                return;
            }
            showNotification('📝 Face registration feature coming soon', 'info');
        }

        function clearFaceData() {
            knownFaces = [];
            detectedFaces = [];
            faceRecognitionStats = {
                totalDetected: 0,
                uniqueUsers: 0,
                accuracy: 0,
                emotionConfidence: 0
            };
            updateFaceStats();
            showNotification('🗑️ Face data cleared', 'success');
        }

        function updateFaceStats() {
            document.getElementById('faces-detected').textContent = faceRecognitionStats.totalDetected;
            document.getElementById('unique-users').textContent = faceRecognitionStats.uniqueUsers;
            document.getElementById('recognition-accuracy').textContent = faceRecognitionStats.accuracy + '%';
            document.getElementById('emotion-confidence').textContent = faceRecognitionStats.emotionConfidence + '%';
        }

        // 🔊 Initialize Audio System
        function initializeAudioSystem() {
            const volumeSlider = document.getElementById('volume-slider');
            const volumeDisplay = document.getElementById('volume-display');

            volumeSlider.addEventListener('input', function() {
                volumeDisplay.textContent = this.value + '%';
            });
        }

        // 📁 Initialize File System
        function initializeFileSystem() {
            const fileDropZone = document.getElementById('fileDropZone');
            const fileInput = document.getElementById('fileInput');
            const clearFilesBtn = document.getElementById('clear-files');
            const downloadFilesBtn = document.getElementById('download-files');

            fileDropZone.addEventListener('click', () => fileInput.click());
            fileDropZone.addEventListener('dragover', handleDragOver);
            fileDropZone.addEventListener('drop', handleFileDrop);
            fileInput.addEventListener('change', handleFileSelect);
            clearFilesBtn.addEventListener('click', clearFiles);
            downloadFilesBtn.addEventListener('click', downloadFiles);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleFileDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            files.forEach(file => {
                uploadedFiles.push({
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    uploadTime: new Date().toISOString()
                });
            });
            updateFileList();
            showNotification(`📁 ${files.length} file(s) uploaded`, 'success');
        }

        function updateFileList() {
            const fileItems = document.getElementById('fileItems');
            if (uploadedFiles.length === 0) {
                fileItems.innerHTML = '<p style="opacity: 0.7; text-align: center;">No hay archivos subidos</p>';
                return;
            }

            fileItems.innerHTML = uploadedFiles.map(file => `
                <div class="file-item">
                    <span>${file.name}</span>
                    <span>${(file.size / 1024).toFixed(1)} KB</span>
                </div>
            `).join('');
        }

        function clearFiles() {
            uploadedFiles = [];
            updateFileList();
            showNotification('🗑️ Files cleared', 'success');
        }

        function downloadFiles() {
            if (uploadedFiles.length === 0) {
                showNotification('⚠️ No files to download', 'warning');
                return;
            }
            showNotification('💾 Download feature coming soon', 'info');
        }

        // 🛒 Initialize Shop System
        function initializeShopSystem() {
            const earnCoinsBtn = document.getElementById('earn-coins');
            const viewInventoryBtn = document.getElementById('view-inventory');
            const buyButtons = document.querySelectorAll('.buy-button');

            earnCoinsBtn.addEventListener('click', earnCoins);
            viewInventoryBtn.addEventListener('click', viewInventory);

            buyButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const itemId = this.dataset.itemId;
                    const price = parseInt(this.dataset.price);
                    const name = this.dataset.name;
                    buyItem(itemId, price, name);
                });
            });

            updateCoinDisplay();
        }

        function earnCoins() {
            const earned = Math.floor(Math.random() * 20) + 10;
            playerCoins += earned;
            updateCoinDisplay();
            showNotification(`💰 Earned ${earned} coins!`, 'success');
        }

        function buyItem(itemId, price, name) {
            if (playerCoins >= price) {
                playerCoins -= price;
                playerInventory.add(itemId);
                updateCoinDisplay();
                showNotification(`🛒 Purchased ${name}!`, 'success');
            } else {
                showNotification('💸 Not enough coins!', 'warning');
            }
        }

        function viewInventory() {
            const items = Array.from(playerInventory);
            if (items.length === 0) {
                showNotification('🎒 Inventory is empty', 'info');
            } else {
                showNotification(`🎒 Inventory: ${items.join(', ')}`, 'info');
            }
        }

        function updateCoinDisplay() {
            document.getElementById('coin-count').textContent = playerCoins;
        }

        // 🎮 Initialize Game Systems
        function initializeGameSystems() {
            const startGameBtn = document.getElementById('start-game');
            const stopGameBtn = document.getElementById('stop-game');
            const resetGameBtn = document.getElementById('reset-game');

            startGameBtn.addEventListener('click', startGame);
            stopGameBtn.addEventListener('click', stopGame);
            resetGameBtn.addEventListener('click', resetGame);

            // Initialize game canvas
            const canvas = document.getElementById('game-canvas');
            if (canvas) {
                const ctx = canvas.getContext('2d');
                drawGameBackground(ctx, canvas.width, canvas.height);
            }
        }

        function startGame() {
            document.getElementById('game-status').textContent = 'Running';
            document.getElementById('ai-status').textContent = 'Active';
            showNotification('🎮 Game started!', 'success');
        }

        function stopGame() {
            document.getElementById('game-status').textContent = 'Stopped';
            document.getElementById('ai-status').textContent = 'Inactive';
            showNotification('⏹️ Game stopped', 'info');
        }

        function resetGame() {
            document.getElementById('score').textContent = '0';
            document.getElementById('level').textContent = '1';
            showNotification('🔄 Game reset', 'info');
        }

        function drawGameBackground(ctx, width, height) {
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, height);
            ctx.fillStyle = '#00ff00';
            ctx.font = '16px Arial';
            ctx.fillText('🤖 Bad Robot Game Engine', 10, 30);
            ctx.fillText('Ready for AI training...', 10, 50);
        }

        // 🐍 Initialize Python Execution
        function initializePythonExecution() {
            const runPythonBtn = document.getElementById('run-python');
            const clearPythonBtn = document.getElementById('clear-python');
            const runTfjsBtn = document.getElementById('run-tfjs');
            const clearTfjsBtn = document.getElementById('clear-tfjs');

            runPythonBtn.addEventListener('click', runPythonCode);
            clearPythonBtn.addEventListener('click', clearPythonCode);
            runTfjsBtn.addEventListener('click', runTensorFlowCode);
            clearTfjsBtn.addEventListener('click', clearTensorFlowCode);

            // Initialize Pyodide
            initializePyodide();

            // Initialize TensorFlow.js
            initializeTensorFlow();
        }

        async function initializePyodide() {
            try {
                document.getElementById('pyodide-status').textContent = 'Loading...';
                pyodide = await loadPyodide();
                document.getElementById('pyodide-status').textContent = 'Ready ✅';
                appendToOutput('🐍 Pyodide loaded successfully!');
            } catch (error) {
                document.getElementById('pyodide-status').textContent = 'Error ❌';
                appendToOutput('❌ Failed to load Pyodide: ' + error.message);
            }
        }

        async function initializeTensorFlow() {
            try {
                document.getElementById('tfjs-status').textContent = 'Loading...';
                await tf.ready();
                tfReady = true;
                document.getElementById('tfjs-status').textContent = 'Ready ✅';
                appendToOutput('🧠 TensorFlow.js loaded successfully!');
            } catch (error) {
                document.getElementById('tfjs-status').textContent = 'Error ❌';
                appendToOutput('❌ Failed to load TensorFlow.js: ' + error.message);
            }
        }

        async function runPythonCode() {
            if (!pyodide) {
                showNotification('⚠️ Pyodide not ready', 'warning');
                return;
            }

            const code = document.getElementById('python-code').value;
            if (!code.trim()) {
                showNotification('⚠️ No Python code to execute', 'warning');
                return;
            }

            try {
                appendToOutput('🐍 Executing Python code...');
                const result = pyodide.runPython(code);
                if (result !== undefined) {
                    appendToOutput('📊 Result: ' + result);
                }
                appendToOutput('✅ Python execution completed');
                showNotification('🐍 Python code executed successfully', 'success');
            } catch (error) {
                appendToOutput('❌ Python Error: ' + error.message);
                showNotification('❌ Python execution failed', 'error');
            }
        }

        function clearPythonCode() {
            document.getElementById('python-code').value = '';
            showNotification('🧹 Python code cleared', 'info');
        }

        async function runTensorFlowCode() {
            if (!tfReady) {
                showNotification('⚠️ TensorFlow.js not ready', 'warning');
                return;
            }

            const code = document.getElementById('tfjs-code').value;
            if (!code.trim()) {
                showNotification('⚠️ No TensorFlow.js code to execute', 'warning');
                return;
            }

            try {
                appendToOutput('🧠 Executing TensorFlow.js code...');
                eval(code);
                appendToOutput('✅ TensorFlow.js execution completed');
                showNotification('🧠 TensorFlow.js code executed successfully', 'success');
            } catch (error) {
                appendToOutput('❌ TensorFlow.js Error: ' + error.message);
                showNotification('❌ TensorFlow.js execution failed', 'error');
            }
        }

        function clearTensorFlowCode() {
            document.getElementById('tfjs-code').value = '';
            showNotification('🧹 TensorFlow.js code cleared', 'info');
        }

        function appendToOutput(text) {
            const output = document.getElementById('execution-output');
            output.innerHTML += text + '<br>';
            output.scrollTop = output.scrollHeight;
        }

        // 📊 Initialize Charts
        function initializeCharts() {
            // Initialize Chart.js charts
            if (typeof Chart !== 'undefined') {
                initializePerformanceChart();
                initializeNetworkChart();
                initializeTrainingChart();
            }
        }

        function initializePerformanceChart() {
            const ctx = document.getElementById('performance-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1m', '2m', '3m', '4m', '5m'],
                        datasets: [{
                            label: 'CPU Usage',
                            data: [45, 52, 48, 61, 55],
                            borderColor: 'rgb(75, 192, 192)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        function initializeNetworkChart() {
            const ctx = document.getElementById('network-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Active', 'Idle', 'Processing'],
                        datasets: [{
                            data: [60, 25, 15],
                            backgroundColor: ['#4CAF50', '#FFC107', '#2196F3']
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        function initializeTrainingChart() {
            const ctx = document.getElementById('training-chart');
            if (ctx) {
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Ep 1', 'Ep 2', 'Ep 3', 'Ep 4', 'Ep 5'],
                        datasets: [{
                            label: 'Reward',
                            data: [12, 19, 23, 25, 28],
                            backgroundColor: 'rgba(54, 162, 235, 0.2)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }

        // 📊 System Status Updates
        function updateSystemStatus() {
            document.getElementById('agents-count').textContent = '6';
            document.getElementById('modules-count').textContent = '12';
            document.getElementById('hub-status').textContent = 'Connected ✅';
            document.getElementById('ai-mode').textContent = 'Intelligent';
        }

        function updateSystemUptime() {
            const uptime = formatUptime(Date.now() - startTime);
            document.getElementById('system-uptime').textContent = uptime;
        }

            // Initialize timestamp display
            const timestampElements = document.querySelectorAll('.timestamp');
            const currentTime = new Date().toLocaleTimeString();
            timestampElements.forEach(el => el.textContent = currentTime);

            // Initialize all systems
            initializeSocket();
            initializeAILibraries();
            initializeVisionSystem();
            initializeFaceRecognition();
            initializeAudioSystem();
            initializeGames();
            initializeFileManagement();
            initializeShopSystem();
            initializeCharts();
            initializeEventListeners();

            // Initialize robot status simulation
            updateRobotStatus();
            setInterval(updateRobotStatus, 5000);

            // Initialize uptime counter
            updateUptime();
            setInterval(updateUptime, 1000);

            showNotification('🤖 Bad Robot AGI Complete System iniciado correctamente', 'success');
            console.log('✅ Bad Robot AGI Complete System Ready!');
        });

        // 🔌 Initialize Socket.IO Connection
        function initializeSocket() {
            try {
                socket = io();

                socket.on('connect', () => {
                    document.getElementById('connection-status').textContent = 'Connected ✅';
                    document.getElementById('connection-status').style.color = 'var(--success)';
                    addEventToList('🔌 Conectado al servidor');
                    console.log('Socket.IO conectado al servidor.');
                });

                socket.on('disconnect', () => {
                    document.getElementById('connection-status').textContent = 'Disconnected ❌';
                    document.getElementById('connection-status').style.color = 'var(--danger)';
                    addEventToList('⚠️ Desconectado del servidor');
                });

                socket.on('system_status', (data) => {
                    addChatMessage(`[Sistema] ${data.message}`, 'system');
                });

                socket.on('agi_response', (data) => {
                    addChatMessage(data.text, 'agi');
                });

                socket.on('audio_response', (data) => {
                    playAudioResponse(data.audio_base64);
                });

                socket.on('gguf_models_list', (data) => {
                    updateModelsList(data.models);
                });

                socket.on('model_loaded', (data) => {
                    updateModelStatus(data);
                });

                socket.on('image_processed_details', (data) => {
                    if (data.error) {
                        addChatMessage(`[Error de Imagen] ${data.error}`, 'system');
                    } else {
                        addChatMessage(`[Análisis de Imagen] ${data.description}`, 'agi');
                    }
                });

            } catch (error) {
                console.error('Error initializing Socket.IO:', error);
                showNotification('❌ Error de conexión Socket.IO', 'error');
            }
        }

        // 🐍 Initialize AI Libraries
        async function initializeAILibraries() {
            try {
                // Initialize TensorFlow.js
                await tf.ready();
                tfReady = true;
                document.getElementById('tfjs-status').textContent = 'Ready ✅';
                document.getElementById('tfjs-status').style.color = 'var(--success)';
                logToConsole('🧠 TensorFlow.js initialized successfully');

                // Initialize Pyodide
                pyodide = await loadPyodide();
                await pyodide.loadPackage(['numpy', 'matplotlib']);
                document.getElementById('pyodide-status').textContent = 'Ready ✅';
                document.getElementById('pyodide-status').style.color = 'var(--success)';
                logToConsole('🐍 Pyodide initialized with numpy and matplotlib');

                showNotification('🚀 AI Libraries loaded successfully!', 'success');
                addEventToList('🧠 Librerías de IA inicializadas');

            } catch (error) {
                console.error('Error initializing AI libraries:', error);
                document.getElementById('pyodide-status').textContent = 'Error ❌';
                document.getElementById('tfjs-status').textContent = 'Error ❌';
                logToConsole('❌ Error loading AI libraries: ' + error.message);
                showNotification('⚠️ Some AI libraries failed to load', 'warning');
            }
        }

        // 👁️ Initialize Vision System
        async function initializeVisionSystem() {
            try {
                const cameraFeed = document.getElementById('camera-feed');
                const detectionOverlay = document.getElementById('detection-overlay');

                if (cameraFeed && detectionOverlay) {
                    console.log('✅ Vision system elements found');
                    addEventToList('👁️ Sistema de visión inicializado');
                }
            } catch (error) {
                console.error('Error initializing vision system:', error);
                showNotification('❌ Error en sistema de visión', 'error');
            }
        }

        // 👤 Initialize Face Recognition
        async function initializeFaceRecognition() {
            try {
                videoElement = document.getElementById('face-video');
                overlayCanvas = document.getElementById('face-overlay');

                if (videoElement && overlayCanvas) {
                    overlayContext = overlayCanvas.getContext('2d');
                    console.log('✅ Face recognition system initialized');
                    addEventToList('👤 Sistema de reconocimiento facial inicializado');
                }

                // Load face-api.js models
                await faceapi.nets.tinyFaceDetector.loadFromUri('/models');
                await faceapi.nets.faceLandmark68Net.loadFromUri('/models');
                await faceapi.nets.faceRecognitionNet.loadFromUri('/models');
                await faceapi.nets.faceExpressionNet.loadFromUri('/models');

                isModelLoaded = true;
                showNotification('👤 Face recognition models loaded', 'success');

            } catch (error) {
                console.error('Error initializing face recognition:', error);
                showNotification('⚠️ Face recognition models not available', 'warning');
            }
        }

        // 🔊 Initialize Audio System
        function initializeAudioSystem() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                audioAnalyser = audioContext.createAnalyser();
                audioAnalyser.fftSize = 256;
                audioVisualizerBufferLength = audioAnalyser.frequencyBinCount;
                audioVisualizerDataArray = new Uint8Array(audioVisualizerBufferLength);
                audioAnalyser.connect(audioContext.destination);

                drawAudioVisualizer();
                initWebSpeechAPI();

                console.log('✅ Audio system initialized');
                addEventToList('🔊 Sistema de audio inicializado');

            } catch (error) {
                console.error("Error inicializando AudioContext:", error);
                showNotification("AudioContext no soportado.", "error");
            }
        }

        // 🎤 Initialize Web Speech API
        function initWebSpeechAPI() {
            if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
                const SpeechRecognitionAPI = window.SpeechRecognition || window.webkitSpeechRecognition;
                speechRecognition = new SpeechRecognitionAPI();
                speechRecognition.lang = 'es-ES';
                speechRecognition.continuous = false;
                speechRecognition.interimResults = false;

                speechRecognition.onstart = () => {
                    const micButton = document.getElementById('mic-button');
                    micButton.classList.add('recording');
                    micButton.textContent = '🎤 Grabando...';
                };

                speechRecognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript.trim();
                    document.getElementById('chat-input').value = transcript;
                    sendMessage();
                };

                speechRecognition.onerror = (event) => {
                    console.error('Error de reconocimiento de voz:', event.error);
                    showNotification('Error en reconocimiento de voz', 'error');
                };

                speechRecognition.onend = () => {
                    const micButton = document.getElementById('mic-button');
                    micButton.classList.remove('recording');
                    micButton.textContent = '🎤 Voz';
                };

                console.log('✅ Web Speech API initialized');
            } else {
                console.warn('Web Speech API not supported');
                const micButton = document.getElementById('mic-button');
                if (micButton) {
                    micButton.disabled = true;
                    micButton.title = 'Reconocimiento de voz no soportado';
                }
            }
        }

        // 🎨 Draw Audio Visualizer
        function drawAudioVisualizer() {
            const audioVisualizerCanvas = document.getElementById('audioVisualizer');
            if (!audioVisualizerCanvas || !audioAnalyser) return;

            const audioVisualizerCtx = audioVisualizerCanvas.getContext('2d');
            audioAnalyser.getByteFrequencyData(audioVisualizerDataArray);

            audioVisualizerCtx.fillStyle = 'var(--card-color)';
            audioVisualizerCtx.fillRect(0, 0, audioVisualizerCanvas.width, audioVisualizerCanvas.height);

            const barWidth = (audioVisualizerCanvas.width / audioVisualizerBufferLength) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < audioVisualizerBufferLength; i++) {
                barHeight = audioVisualizerDataArray[i] / 2;
                audioVisualizerCtx.fillStyle = `rgb(${barHeight + 100},50,50)`;
                audioVisualizerCtx.fillRect(x, audioVisualizerCanvas.height - barHeight, barWidth, barHeight);
                x += barWidth + 1;
            }
            requestAnimationFrame(drawAudioVisualizer);
        }

        // 🎮 Initialize Games
        function initializeGames() {
            console.log('🎮 Initializing games...');

            const canvas1 = document.getElementById('game-canvas');
            const canvas2 = document.getElementById('dqn-game-canvas2');

            if (canvas1) {
                setupGameCanvas(canvas1, 'game1');
            }
            if (canvas2) {
                setupGameCanvas(canvas2, 'game2');
            }

            setupGameEventListeners();
            addEventToList('🎮 Juegos inicializados');
        }

        function setupGameCanvas(canvas, gameId) {
            const ctx = canvas.getContext('2d');
            canvas.style.border = '2px solid var(--primary)';
            canvas.style.borderRadius = 'var(--border-radius)';
            canvas.style.background = '#1a1a1a';

            ctx.fillStyle = '#333';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.fillStyle = '#fff';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(`🎮 DQN Game ${gameId === 'game1' ? '1' : '2'}`, canvas.width/2, 30);
            ctx.fillText('Click "Iniciar Juego" to start', canvas.width/2, 60);

            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(50, 100, 20, 20);
            ctx.fillStyle = '#f44336';
            ctx.fillRect(300, 150, 15, 15);
        }

        function setupGameEventListeners() {
            // Game controls event listeners would go here
            // This is a simplified version for the complete interface
        }

        // 📁 Initialize File Management
        function initializeFileManagement() {
            const fileDropZone = document.getElementById('fileDropZone');
            const fileInput = document.getElementById('fileInput');

            if (fileDropZone && fileInput) {
                fileDropZone.addEventListener('click', () => fileInput.click());

                fileDropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    fileDropZone.classList.add('dragover');
                });

                fileDropZone.addEventListener('dragleave', () => {
                    fileDropZone.classList.remove('dragover');
                });

                fileDropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    fileDropZone.classList.remove('dragover');
                    if (e.dataTransfer.files.length > 0) {
                        handleFiles(e.dataTransfer.files);
                    }
                });

                fileInput.addEventListener('change', (e) => {
                    if (e.target.files.length > 0) {
                        handleFiles(e.target.files);
                    }
                });

                console.log('✅ File management initialized');
                addEventToList('📁 Gestión de archivos inicializada');
            }
        }

        function handleFiles(files) {
            Array.from(files).forEach(file => {
                uploadedFiles.push(file);
                addFileToList(file);
            });
            updateFileDisplay();
        }

        function addFileToList(file) {
            const fileItems = document.getElementById('fileItems');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span>${file.name}</span>
                <span>${(file.size/1024).toFixed(2)} KB</span>
            `;
            fileItems.appendChild(fileItem);
        }

        function updateFileDisplay() {
            const fileItems = document.getElementById('fileItems');
            if (uploadedFiles.length === 0) {
                fileItems.innerHTML = '<p style="opacity: 0.7; text-align: center;">No hay archivos subidos</p>';
            }
        }

        // 🛒 Initialize Shop System
        function initializeShopSystem() {
            const shopItems = document.getElementById('shop-items');
            if (shopItems) {
                shopItems.addEventListener('click', (e) => {
                    if (e.target.classList.contains('buy-button')) {
                        const itemId = e.target.dataset.itemId;
                        const price = parseInt(e.target.dataset.price);
                        const itemName = e.target.dataset.name;

                        if (playerCoins >= price) {
                            playerCoins -= price;
                            playerInventory.add(itemId);
                            updateCoinDisplay();
                            showNotification(`✅ ${itemName} comprado!`, 'success');
                            addEventToList(`🛒 Comprado: ${itemName}`);
                        } else {
                            showNotification(`❌ No tienes suficientes monedas para ${itemName}`, 'warning');
                        }
                    }
                });

                console.log('✅ Shop system initialized');
                addEventToList('🛒 Sistema de tienda inicializado');
            }
        }

        function updateCoinDisplay() {
            const coinCount = document.getElementById('coin-count');
            if (coinCount) {
                coinCount.textContent = playerCoins;
            }
        }

        // 📊 Initialize Charts
        function initializeCharts() {
            try {
                // Performance Chart
                const performanceCtx = document.getElementById('performance-chart');
                if (performanceCtx) {
                    new Chart(performanceCtx, {
                        type: 'line',
                        data: {
                            labels: ['1m', '2m', '3m', '4m', '5m'],
                            datasets: [{
                                label: 'CPU Usage',
                                data: [45, 52, 48, 61, 55],
                                borderColor: 'rgb(75, 192, 192)',
                                tension: 0.1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }

                // Network Chart
                const networkCtx = document.getElementById('network-chart');
                if (networkCtx) {
                    new Chart(networkCtx, {
                        type: 'doughnut',
                        data: {
                            labels: ['Activo', 'Inactivo', 'Procesando'],
                            datasets: [{
                                data: [65, 25, 10],
                                backgroundColor: ['#4CAF50', '#f44336', '#ff9800']
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false
                        }
                    });
                }

                console.log('✅ Charts initialized');
                addEventToList('📊 Gráficos inicializados');

            } catch (error) {
                console.error('Error initializing charts:', error);
            }
        }

        // 🎛️ Initialize Event Listeners
        function initializeEventListeners() {
            // Chat functionality
            const sendButton = document.getElementById('send-button');
            const chatInput = document.getElementById('chat-input');
            const micButton = document.getElementById('mic-button');

            if (sendButton) {
                sendButton.addEventListener('click', sendMessage);
            }

            if (chatInput) {
                chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                    }
                });
            }

            if (micButton && speechRecognition) {
                micButton.addEventListener('click', () => {
                    if (micButton.classList.contains('recording')) {
                        speechRecognition.stop();
                    } else {
                        speechRecognition.start();
                    }
                });
            }

            // Vision system controls
            const toggleCamera = document.getElementById('toggle-camera');
            if (toggleCamera) {
                toggleCamera.addEventListener('click', toggleCameraFeed);
            }

            // Face recognition controls
            const startFaceRec = document.getElementById('start-face-recognition');
            const stopFaceRec = document.getElementById('stop-face-recognition');

            if (startFaceRec) {
                startFaceRec.addEventListener('click', startFaceRecognition);
            }
            if (stopFaceRec) {
                stopFaceRec.addEventListener('click', stopFaceRecognition);
            }

            // Python execution
            const runPython = document.getElementById('run-python');
            const runTfjs = document.getElementById('run-tfjs');

            if (runPython) {
                runPython.addEventListener('click', executePythonCode);
            }
            if (runTfjs) {
                runTfjs.addEventListener('click', executeTensorFlowCode);
            }

            // Shop system
            const earnCoins = document.getElementById('earn-coins');
            if (earnCoins) {
                earnCoins.addEventListener('click', () => {
                    playerCoins += 25;
                    updateCoinDisplay();
                    showNotification('💰 +25 monedas ganadas!', 'success');
                });
            }

            console.log('✅ Event listeners initialized');
        }

        // 💬 Chat Functions
        function sendMessage() {
            const chatInput = document.getElementById('chat-input');
            const message = chatInput.value.trim();

            if (!message) return;

            addChatMessage(message, 'user');
            chatInput.value = '';

            // Process message with Professional Neural-Symbolic Agent if available
            if (neuralSymbolicAgent && neuralSymbolicAgent.isActive) {
                processMessageWithCompleteProfessionalAgent(message);
            } else {
                // Fallback to original processing
                processMessage(message);
            }
        }

        async function processMessage(message) {
            try {
                if (socket && socket.connected) {
                    socket.emit('text_message', { text: message });
                } else {
                    // Fallback local processing
                    const response = await generateLocalResponse(message);
                    addChatMessage(response, 'agi');
                }
            } catch (error) {
                console.error('Error processing message:', error);
                addChatMessage('❌ Error procesando mensaje', 'system');
            }
        }

        async function generateLocalResponse(message) {
            const responses = [
                `Entiendo tu mensaje: "${message}". Como sistema AGI completo, estoy procesando tu solicitud.`,
                `Procesando con capacidades avanzadas de IA. El sistema completo incluye visión, audio, reconocimiento facial y más.`,
                `Como tu asistente Bad Robot AGI, estoy listo para ayudarte con tareas complejas usando IA multimodal.`,
                `Analizando tu solicitud con todos los módulos disponibles: visión, audio, juegos, y procesamiento de datos.`
            ];
            return responses[Math.floor(Math.random() * responses.length)];
        }

        function addChatMessage(text, sender = 'agi') {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const senderName = sender === 'user' ? '👤 Usuario' :
                              sender === 'agi' ? '🤖 Bad Robot AGI' :
                              sender === 'face-recognition' ? '👁️ Face Recognition' :
                              '🔧 Sistema';

            messageDiv.innerHTML = `
                <div class="message-header">
                    <span class="sender">${senderName}</span>
                    <span class="timestamp">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="message-content">${text}</div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 👁️ Vision System Functions
        async function toggleCameraFeed() {
            const cameraFeed = document.getElementById('camera-feed');
            const toggleButton = document.getElementById('toggle-camera');

            if (!visionActive) {
                try {
                    const constraints = {
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'user'
                        },
                        audio: false
                    };
                    cameraStream = await navigator.mediaDevices.getUserMedia(constraints);
                    cameraFeed.srcObject = cameraStream;
                    await cameraFeed.play();

                    visionActive = true;
                    toggleButton.textContent = '📹 Detener Cámara';
                    showNotification('📹 Cámara iniciada', 'success');
                    addEventToList('📹 Cámara activada');

                } catch (error) {
                    console.error('Error accessing camera:', error);
                    showNotification('❌ Error accediendo a la cámara', 'error');
                }
            } else {
                if (cameraStream) {
                    cameraStream.getTracks().forEach(track => track.stop());
                    cameraFeed.srcObject = null;
                }
                visionActive = false;
                toggleButton.textContent = '📹 Iniciar Cámara';
                showNotification('📹 Cámara detenida', 'info');
                addEventToList('📹 Cámara desactivada');
            }
        }

        // 👤 Face Recognition Functions
        async function startFaceRecognition() {
            if (!isModelLoaded) {
                showNotification('❌ Modelos de reconocimiento facial no cargados', 'error');
                return;
            }

            try {
                const constraints = { video: { facingMode: 'user' }, audio: false };
                stream = await navigator.mediaDevices.getUserMedia(constraints);
                videoElement.srcObject = stream;
                await videoElement.play();

                overlayCanvas.width = videoElement.videoWidth;
                overlayCanvas.height = videoElement.videoHeight;

                faceDetectionInterval = setInterval(detectFaces, 100);
                showNotification('👤 Reconocimiento facial iniciado', 'success');
                addEventToList('👤 Reconocimiento facial activado');

            } catch (error) {
                console.error('Error starting face recognition:', error);
                showNotification('❌ Error iniciando reconocimiento facial', 'error');
            }
        }

        function stopFaceRecognition() {
            if (faceDetectionInterval) {
                clearInterval(faceDetectionInterval);
                faceDetectionInterval = null;
            }

            if (stream) {
                stream.getTracks().forEach(track => track.stop());
                videoElement.srcObject = null;
            }

            overlayContext.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
            showNotification('👤 Reconocimiento facial detenido', 'info');
            addEventToList('👤 Reconocimiento facial desactivado');
        }

        async function detectFaces() {
            if (!videoElement || videoElement.paused || videoElement.ended) return;

            try {
                const detections = await faceapi.detectAllFaces(videoElement, new faceapi.TinyFaceDetectorOptions())
                    .withFaceLandmarks()
                    .withFaceExpressions();

                overlayContext.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);

                if (detections.length > 0) {
                    faceRecognitionStats.totalDetected++;
                    updateFaceStats();

                    detections.forEach(detection => {
                        const { x, y, width, height } = detection.detection.box;
                        overlayContext.strokeStyle = '#00ff00';
                        overlayContext.lineWidth = 2;
                        overlayContext.strokeRect(x, y, width, height);

                        // Draw emotion
                        const emotions = detection.expressions;
                        const topEmotion = Object.keys(emotions).reduce((a, b) => emotions[a] > emotions[b] ? a : b);

                        overlayContext.fillStyle = '#00ff00';
                        overlayContext.font = '16px Arial';
                        overlayContext.fillText(topEmotion, x, y - 10);
                    });
                }
            } catch (error) {
                console.error('Error in face detection:', error);
            }
        }

        function updateFaceStats() {
            document.getElementById('faces-detected').textContent = faceRecognitionStats.totalDetected;
            document.getElementById('unique-users').textContent = faceRecognitionStats.uniqueUsers;
            document.getElementById('recognition-accuracy').textContent = faceRecognitionStats.accuracy + '%';
            document.getElementById('emotion-confidence').textContent = faceRecognitionStats.emotionConfidence + '%';
        }

        // 🐍 Python & TensorFlow Execution
        async function executePythonCode() {
            const code = document.getElementById('python-code').value;
            if (!code.trim()) {
                showNotification('⚠️ No Python code to execute', 'warning');
                return;
            }

            if (!pyodide) {
                showNotification('❌ Pyodide not loaded yet', 'error');
                return;
            }

            try {
                logToConsole('🐍 Executing Python code...');

                pyodide.runPython(`
import sys
from io import StringIO
sys.stdout = StringIO()
                `);

                pyodide.runPython(code);
                const output = pyodide.runPython('sys.stdout.getvalue()');

                logToConsole('🐍 Python Output:');
                logToConsole(output || 'No output');
                showNotification('✅ Python code executed successfully', 'success');
                addEventToList('🐍 Código Python ejecutado');

            } catch (error) {
                logToConsole('❌ Python Error: ' + error.message);
                showNotification('❌ Python execution failed', 'error');
            }
        }

        async function executeTensorFlowCode() {
            const code = document.getElementById('tfjs-code').value;
            if (!code.trim()) {
                showNotification('⚠️ No TensorFlow.js code to execute', 'warning');
                return;
            }

            if (!tfReady) {
                showNotification('❌ TensorFlow.js not ready yet', 'error');
                return;
            }

            try {
                logToConsole('🧠 Executing TensorFlow.js code...');
                const result = eval(code);

                if (result && typeof result.then === 'function') {
                    result.then(res => {
                        logToConsole('🧠 TensorFlow.js Result: ' + JSON.stringify(res));
                    }).catch(err => {
                        logToConsole('❌ TensorFlow.js Promise Error: ' + err.message);
                    });
                } else {
                    logToConsole('🧠 TensorFlow.js Result: ' + JSON.stringify(result));
                }

                showNotification('✅ TensorFlow.js code executed successfully', 'success');
                addEventToList('🧠 Código TensorFlow.js ejecutado');

            } catch (error) {
                logToConsole('❌ TensorFlow.js Error: ' + error.message);
                showNotification('❌ TensorFlow.js execution failed', 'error');
            }
        }

        // 📝 Utility Functions
        function logToConsole(message) {
            const output = document.getElementById('execution-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<br>[${timestamp}] ${message}`;
            output.scrollTop = output.scrollHeight;
        }

        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            container.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 3000);
            }, 5000);
        }

        function addEventToList(eventText) {
            const eventsList = document.getElementById('events-list');
            const li = document.createElement('li');
            const timestamp = new Date().toLocaleTimeString();
            li.textContent = `[${timestamp}] ${eventText}`;
            eventsList.insertBefore(li, eventsList.firstChild);

            // Limit to 20 events
            while (eventsList.children.length > 20) {
                eventsList.removeChild(eventsList.lastChild);
            }
        }

        function updateRobotStatus() {
            const energy = Math.floor(Math.random() * 20) + 80;
            const cpu = Math.floor(Math.random() * 30) + 30;
            const ram = Math.floor(Math.random() * 40) + 50;

            document.getElementById('robot-energy').textContent = energy + '%';
            document.getElementById('robot-cpu').textContent = cpu + '%';
            document.getElementById('robot-ram').textContent = ram + '%';

            const energyEl = document.getElementById('robot-energy').parentElement;
            energyEl.style.color = energy > 90 ? 'var(--success)' : energy > 70 ? 'var(--warning)' : 'var(--danger)';
        }

        function updateUptime() {
            const uptime = Date.now() - startTime;
            const hours = Math.floor(uptime / 3600000);
            const minutes = Math.floor((uptime % 3600000) / 60000);
            const seconds = Math.floor((uptime % 60000) / 1000);

            const uptimeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('system-uptime').textContent = uptimeString;
        }

        function updateModelsList(models) {
            const ggufModelSelect = document.getElementById('gguf-models');
            ggufModelSelect.innerHTML = '<option value="">Seleccionar modelo local...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model.path;
                option.textContent = `${model.name} (${(model.size_gb || 0).toFixed(2)}GB)`;
                ggufModelSelect.appendChild(option);
            });
        }

        function updateModelStatus(data) {
            document.getElementById('model-status').textContent = `Activo: ${data.model_name || 'Desconocido'}`;
            document.getElementById('model-status').style.color = 'var(--success)';
            document.getElementById('current-model-name').textContent = data.model_name || 'Desconocido';
            addEventToList(`🧠 Modelo cargado: ${data.model_name}`);
        }

        function playAudioResponse(base64Audio) {
            if (!base64Audio) return;
            const audioPlayer = document.getElementById('audio-player');
            const audioSrc = `data:audio/mpeg;base64,${base64Audio}`;
            audioPlayer.src = audioSrc;
            audioPlayer.style.display = 'block';
            audioPlayer.play().catch(e => console.warn("Audio playback blocked by browser.", e));
        }

        // Add CSS animations for notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .notification {
                animation: slideIn 0.3s ease;
            }
        `;
        document.head.appendChild(style);

        // ================ PROFESSIONAL NEURAL-SYMBOLIC DQN REINFORCEMENT LEARNING AGENT ================

        // Initialize Professional Neural-Symbolic Agent
        async function initializeNeuralSymbolicAgent() {
            console.log('🧠 Initializing Professional Neural-Symbolic DQN Agent for Complete System...');

            try {
                // Initialize TensorFlow.js
                await tf.ready();
                tensorflowReady = true;
                tfReady = true;
                console.log('✅ TensorFlow.js ready for complete system');

                // Initialize the professional neural-symbolic agent
                neuralSymbolicAgent = new NeuralSymbolicAgent();
                await neuralSymbolicAgent.initialize();

                // Setup agent event listeners
                setupAgentEventListeners();

                // Initialize knowledge base
                initializeKnowledgeBase();

                // Start background learning
                startBackgroundLearning();

                console.log('🧠 Professional Neural-Symbolic DQN Agent initialized successfully for complete system');
                showNotification('🧠 Neural-Symbolic Agent ready!', 'success');

            } catch (error) {
                console.error('❌ Error initializing Neural-Symbolic Agent:', error);
                showNotification('Failed to initialize Neural Agent', 'error');
            }
        }

        // Professional Neural-Symbolic Agent Class for Complete System
        class NeuralSymbolicAgent {
            constructor() {
                this.isActive = false;
                this.isAutonomous = false;
                this.learningRate = 0.001;
                this.epsilon = 0.1; // Exploration rate
                this.gamma = 0.95; // Discount factor
                this.memorySize = 15000; // Larger memory for complete system
                this.batchSize = 64; // Larger batch size for complete system
                this.updateFrequency = 150;
                this.stepCount = 0;
                this.episodeCount = 0;

                // Neural networks
                this.qNetwork = null;
                this.targetNetwork = null;

                // Memory buffer
                this.memory = [];

                // Symbolic reasoning
                this.rules = [];
                this.facts = [];
                this.goals = [];

                // Multi-reward system
                this.rewards = {
                    userSatisfaction: 0,
                    taskCompletion: 0,
                    learningProgress: 0,
                    systemEfficiency: 0,
                    knowledgeGrowth: 0
                };

                // Communication
                this.conversationContext = [];
                this.userProfile = null;
                this.systemState = {};
            }

            async initialize() {
                // Initialize TensorFlow.js models
                if (tensorflowReady) {
                    await this.initializeNeuralNetworks();
                }

                // Initialize symbolic reasoning
                this.initializeSymbolicReasoning();

                // Initialize multi-reward system
                this.initializeRewardSystem();

                // Load pre-trained knowledge if available
                this.loadKnowledge();

                this.isActive = true;
                this.updateAgentStatus();
            }

            async initializeNeuralNetworks() {
                try {
                    // Create enhanced Q-Network architecture for complete system
                    this.qNetwork = tf.sequential({
                        layers: [
                            tf.layers.dense({ inputShape: [128], units: 512, activation: 'relu' }), // Larger input and hidden layers
                            tf.layers.dropout({ rate: 0.3 }),
                            tf.layers.dense({ units: 512, activation: 'relu' }),
                            tf.layers.dropout({ rate: 0.3 }),
                            tf.layers.dense({ units: 256, activation: 'relu' }),
                            tf.layers.dense({ units: 128, activation: 'relu' }),
                            tf.layers.dense({ units: 64, activation: 'linear' }) // Larger action space
                        ]
                    });

                    // Create enhanced Target Network
                    this.targetNetwork = tf.sequential({
                        layers: [
                            tf.layers.dense({ inputShape: [128], units: 512, activation: 'relu' }),
                            tf.layers.dropout({ rate: 0.3 }),
                            tf.layers.dense({ units: 512, activation: 'relu' }),
                            tf.layers.dropout({ rate: 0.3 }),
                            tf.layers.dense({ units: 256, activation: 'relu' }),
                            tf.layers.dense({ units: 128, activation: 'relu' }),
                            tf.layers.dense({ units: 64, activation: 'linear' })
                        ]
                    });

                    // Compile models
                    this.qNetwork.compile({
                        optimizer: tf.train.adam(this.learningRate),
                        loss: 'meanSquaredError'
                    });

                    this.targetNetwork.compile({
                        optimizer: tf.train.adam(this.learningRate),
                        loss: 'meanSquaredError'
                    });

                    // Copy weights from Q-Network to Target Network
                    this.updateTargetNetwork();

                    console.log('🧠 Professional enhanced neural networks initialized for complete system');

                } catch (error) {
                    console.log(`❌ Neural network initialization error: ${error.message}`);
                    throw error;
                }
            }

            initializeSymbolicReasoning() {
                // Initialize comprehensive professional symbolic rules
                this.rules = [
                    {
                        id: 'user_greeting',
                        condition: (context) => context.includes('hello') || context.includes('hi') || context.includes('hola'),
                        action: 'respond_greeting',
                        priority: 1
                    },
                    {
                        id: 'help_request',
                        condition: (context) => context.includes('help') || context.includes('assist') || context.includes('ayuda'),
                        action: 'provide_assistance',
                        priority: 2
                    },
                    {
                        id: 'search_request',
                        condition: (context) => context.includes('search') || context.includes('find') || context.includes('buscar'),
                        action: 'perform_search',
                        priority: 3
                    },
                    {
                        id: 'learning_opportunity',
                        condition: (context) => context.includes('learn') || context.includes('teach') || context.includes('aprender'),
                        action: 'engage_learning',
                        priority: 2
                    },
                    {
                        id: 'face_recognition',
                        condition: (context) => currentUser !== null,
                        action: 'personalize_response',
                        priority: 1
                    },
                    {
                        id: 'professional_query',
                        condition: (context) => context.includes('professional') || context.includes('business') || context.includes('work'),
                        action: 'professional_response',
                        priority: 2
                    },
                    {
                        id: 'game_request',
                        condition: (context) => context.includes('game') || context.includes('play') || context.includes('juego'),
                        action: 'gaming_assistance',
                        priority: 2
                    },
                    {
                        id: 'file_management',
                        condition: (context) => context.includes('file') || context.includes('upload') || context.includes('archivo'),
                        action: 'file_assistance',
                        priority: 2
                    }
                ];

                // Initialize comprehensive professional facts
                this.facts = [
                    'I am a professional neural-symbolic AI agent for complete system management',
                    'I can learn from interactions and provide expert assistance across all modules',
                    'I have access to advanced face recognition and emotion analysis capabilities',
                    'I can perform professional web searches and comprehensive analysis',
                    'I can execute complex reasoning and multi-modal problem-solving tasks',
                    'I use enhanced multi-reward reinforcement learning for optimal performance',
                    'I provide personalized responses based on comprehensive user recognition',
                    'I continuously improve through autonomous learning across all system components',
                    'I can assist with gaming, file management, and system administration',
                    'I integrate with all Bad Robot AGI system modules for comprehensive assistance'
                ];

                console.log('🧠 Professional comprehensive symbolic reasoning system initialized');
            }

            initializeRewardSystem() {
                // Reset all rewards
                Object.keys(this.rewards).forEach(key => {
                    this.rewards[key] = 0;
                });

                console.log('🏆 Professional comprehensive multi-reward system initialized');
            }

            loadKnowledge() {
                // Load knowledge from localStorage if available
                const savedKnowledge = localStorage.getItem('professionalCompleteNeuralAgentKnowledge');
                if (savedKnowledge) {
                    try {
                        const knowledge = JSON.parse(savedKnowledge);
                        this.facts = [...this.facts, ...knowledge.facts];
                        this.conversationContext = knowledge.conversationContext || [];

                        console.log(`📚 Loaded ${knowledge.facts.length} professional complete knowledge items from storage`);
                    } catch (error) {
                        console.log(`❌ Error loading knowledge: ${error.message}`);
                    }
                }
            }

            saveKnowledge() {
                // Save knowledge to localStorage
                const knowledge = {
                    facts: this.facts,
                    conversationContext: this.conversationContext,
                    rewards: this.rewards,
                    episodeCount: this.episodeCount
                };

                localStorage.setItem('professionalCompleteNeuralAgentKnowledge', JSON.stringify(knowledge));
                console.log('💾 Professional complete knowledge saved to storage');
            }

            // Process user message with professional neural-symbolic reasoning for complete system
            async processMessage(message, userContext = null) {
                try {
                    // Add to conversation context
                    this.conversationContext.push({
                        type: 'user',
                        message: message,
                        timestamp: Date.now(),
                        userContext: userContext
                    });

                    // Symbolic reasoning - apply rules
                    const applicableRules = this.applySymbolicRules(message.toLowerCase());

                    // Neural processing - get enhanced state representation
                    const state = this.getEnhancedStateRepresentation(message, userContext);

                    // DQN action selection
                    const action = await this.selectAction(state);

                    // Generate comprehensive professional response
                    const response = await this.generateComprehensiveProfessionalResponse(message, action, applicableRules, userContext);

                    // Calculate rewards
                    const reward = this.calculateReward(message, response, action);

                    // Store experience in memory
                    this.storeExperience(state, action, reward, this.getEnhancedStateRepresentation(response, userContext));

                    // Update rewards
                    this.updateRewards(reward);

                    // Learn from experience
                    if (this.memory.length >= this.batchSize) {
                        await this.trainDQN();
                    }

                    // Add to conversation context
                    this.conversationContext.push({
                        type: 'agent',
                        message: response,
                        timestamp: Date.now(),
                        action: action,
                        reward: reward
                    });

                    // Update agent status
                    this.updateAgentStatus();

                    // Save knowledge periodically
                    if (this.stepCount % 15 === 0) {
                        this.saveKnowledge();
                    }

                    this.stepCount++;

                    return response;

                } catch (error) {
                    console.log(`❌ Error processing message in complete system: ${error.message}`);
                    return "I encountered an error processing your message in the complete system. Let me provide professional assistance differently.";
                }
            }

            applySymbolicRules(message) {
                const applicableRules = [];

                for (const rule of this.rules) {
                    try {
                        if (rule.condition(message)) {
                            applicableRules.push(rule);
                        }
                    } catch (error) {
                        console.log(`❌ Error applying rule ${rule.id}: ${error.message}`);
                    }
                }

                // Sort by priority
                applicableRules.sort((a, b) => b.priority - a.priority);

                return applicableRules;
            }

            getEnhancedStateRepresentation(text, userContext) {
                // Create a 128-dimensional state vector for complete system
                const state = new Array(128).fill(0);

                // Text features (first 64 dimensions)
                const words = text.toLowerCase().split(' ');
                const features = [
                    'hello', 'help', 'search', 'learn', 'thank', 'please', 'question', 'problem',
                    'good', 'bad', 'yes', 'no', 'maybe', 'sure', 'sorry', 'great',
                    'what', 'how', 'why', 'when', 'where', 'who', 'which', 'can',
                    'will', 'would', 'could', 'should', 'might', 'must', 'need', 'want',
                    'game', 'play', 'file', 'upload', 'download', 'save', 'load', 'create',
                    'delete', 'modify', 'analyze', 'calculate', 'translate', 'explain', 'show', 'display',
                    'professional', 'business', 'work', 'project', 'task', 'complete', 'finish', 'done',
                    'error', 'fix', 'solve', 'resolve', 'issue', 'bug', 'debug', 'test'
                ];

                features.forEach((feature, index) => {
                    if (index < 64) {
                        state[index] = words.includes(feature) ? 1 : 0;
                    }
                });

                // User context features (dimensions 64-95)
                if (userContext) {
                    state[64] = 1; // User authenticated
                    state[65] = Math.min(userContext.interactions / 100, 1); // Interaction history
                    state[66] = userContext.role === 'Admin' ? 1 : 0; // User role
                    state[67] = userContext.preferences?.language === 'en' ? 1 : 0; // Language preference
                    state[68] = currentUser?.emotions?.confidence || 0; // Emotion confidence
                    state[69] = faceRecognitionEnabled ? 1 : 0; // Face recognition status
                }

                // System state features (dimensions 96-127)
                state[96] = this.isAutonomous ? 1 : 0;
                state[97] = Math.min(this.rewards.userSatisfaction / 100, 1);
                state[98] = Math.min(this.rewards.taskCompletion / 100, 1);
                state[99] = Math.min(this.rewards.learningProgress / 100, 1);
                state[100] = Math.min(this.rewards.systemEfficiency / 100, 1);
                state[101] = Math.min(this.rewards.knowledgeGrowth / 100, 1);
                state[102] = Math.min(this.facts.length / 1000, 1);
                state[103] = Math.min(this.conversationContext.length / 100, 1);
                state[104] = webSearchEnabled ? 1 : 0;
                state[105] = tfReady ? 1 : 0;
                state[106] = pyodide ? 1 : 0;
                state[107] = emotionAnalysisEnabled ? 1 : 0;

                return tf.tensor2d([state]);
            }

            async selectAction(state) {
                // Epsilon-greedy action selection with larger action space
                if (Math.random() < this.epsilon) {
                    // Random action (exploration)
                    return Math.floor(Math.random() * 64);
                } else {
                    // Best action from Q-Network (exploitation)
                    if (this.qNetwork) {
                        const qValues = this.qNetwork.predict(state);
                        const actionIndex = tf.argMax(qValues, 1).dataSync()[0];
                        qValues.dispose();
                        return actionIndex;
                    } else {
                        return Math.floor(Math.random() * 64);
                    }
                }
            }

            async generateComprehensiveProfessionalResponse(message, action, rules, userContext) {
                // Map actions to comprehensive professional response types
                const actionMap = {
                    0: 'greeting', 1: 'help', 2: 'search', 3: 'learn', 4: 'clarify',
                    5: 'confirm', 6: 'deny', 7: 'suggest', 8: 'explain', 9: 'question',
                    10: 'compliment', 11: 'apologize', 12: 'encourage', 13: 'inform', 14: 'request',
                    15: 'analyze', 16: 'compare', 17: 'summarize', 18: 'predict', 19: 'recommend',
                    20: 'execute', 21: 'calculate', 22: 'translate', 23: 'create', 24: 'modify',
                    25: 'delete', 26: 'save', 27: 'load', 28: 'export', 29: 'import',
                    30: 'autonomous', 31: 'personalize', 32: 'gaming', 33: 'file_management',
                    34: 'face_recognition', 35: 'emotion_analysis', 36: 'system_management', 37: 'diagnostics',
                    38: 'optimization', 39: 'training', 40: 'monitoring', 41: 'security',
                    42: 'integration', 43: 'collaboration', 44: 'innovation', 45: 'research',
                    46: 'development', 47: 'testing', 48: 'deployment', 49: 'maintenance',
                    50: 'support', 51: 'consultation', 52: 'analysis', 53: 'reporting',
                    54: 'visualization', 55: 'automation', 56: 'customization', 57: 'enhancement',
                    58: 'troubleshooting', 59: 'backup', 60: 'recovery', 61: 'upgrade',
                    62: 'configuration', 63: 'professional_comprehensive'
                };

                const responseType = actionMap[action] || 'help';
                const userName = userContext?.name || 'Usuario';

                // Check if we should perform comprehensive web search
                if (rules.some(rule => rule.action === 'perform_search') || responseType === 'search') {
                    const searchResult = await this.performComprehensiveProfessionalWebSearch(message);
                    return searchResult;
                }

                // Check if we should provide comprehensive professional AI assistance
                if (message.toLowerCase().includes('complex') || message.toLowerCase().includes('professional') ||
                    message.toLowerCase().includes('business') || message.toLowerCase().includes('advanced')) {
                    const aiResponse = await this.provideComprehensiveProfessionalAIAssistance(message, userContext);
                    return aiResponse;
                }

                // Generate comprehensive professional response based on type and rules
                const responses = this.getComprehensiveProfessionalResponseTemplates(responseType, userName, userContext);
                const selectedResponse = responses[Math.floor(Math.random() * responses.length)];

                // Add learning to facts
                this.addToKnowledge(`User asked: "${message}"`);
                this.addToKnowledge(`Agent responded with ${responseType} action professionally in complete system`);

                return selectedResponse;
            }

            getComprehensiveProfessionalResponseTemplates(responseType, userName, userContext) {
                const templates = {
                    greeting: [
                        `Buenos días ${userName}. Soy su asistente de IA neural-simbólico profesional para el sistema completo Bad Robot AGI. ¿En qué puedo ayudarle hoy?`,
                        `Hola ${userName}. Estoy aquí para brindarle asistencia profesional integral con todas las capacidades del sistema Bad Robot AGI.`,
                        `Saludos ${userName}. Mi sistema neural-simbólico completo está listo para proporcionarle el mejor servicio profesional en todas las áreas.`
                    ],
                    help: [
                        `Estaré encantado de ayudarle profesionalmente, ${userName}. Tengo acceso completo a reconocimiento facial, análisis de emociones, búsquedas web, gestión de archivos, juegos, y todas las capacidades del sistema Bad Robot AGI.`,
                        `Permítame asistirle con el sistema completo. Puedo ayudarle con análisis facial, procesamiento de emociones, búsquedas avanzadas, gestión de archivos, entretenimiento y mucho más.`,
                        `Estoy aquí para ayudarle profesionalmente con todas las capacidades del sistema. Desde reconocimiento facial hasta gestión completa del sistema Bad Robot AGI.`
                    ],
                    search: [
                        `Realizaré una búsqueda web profesional integral utilizando todas las capacidades del sistema para proporcionarle información completa y actualizada.`,
                        `Utilizaré las capacidades avanzadas de búsqueda del sistema completo para encontrar información profesional y confiable.`,
                        `Ejecutando búsqueda profesional con análisis integral del sistema Bad Robot AGI para obtener los mejores resultados.`
                    ],
                    gaming: [
                        `Puedo asistirle con los juegos integrados en el sistema Bad Robot AGI, incluyendo análisis de rendimiento y optimización de la experiencia de juego.`,
                        `El sistema de juegos está disponible con capacidades de IA avanzadas. ¿Le gustaría explorar las opciones de entretenimiento?`,
                        `Ofrezco asistencia completa para los módulos de juegos con integración de reconocimiento facial y análisis de emociones para una experiencia personalizada.`
                    ],
                    file_management: [
                        `Puedo ayudarle con la gestión completa de archivos, incluyendo carga, descarga, análisis y procesamiento con las capacidades avanzadas del sistema.`,
                        `El sistema de gestión de archivos está integrado con IA para análisis automático y organización inteligente. ¿Qué necesita procesar?`,
                        `Ofrezco servicios profesionales de gestión de archivos con análisis automático, categorización y procesamiento inteligente.`
                    ],
                    face_recognition: [
                        `El sistema de reconocimiento facial está activo y funcionando con precisión profesional. Puedo personalizar la experiencia basándome en su perfil.`,
                        `Utilizando reconocimiento facial avanzado para proporcionar servicios personalizados y seguros. Su identidad ha sido verificada profesionalmente.`,
                        `El módulo de reconocimiento facial está operativo con capacidades de análisis emocional integradas para una experiencia completa.`
                    ],
                    system_management: [
                        `Proporcionando gestión completa del sistema Bad Robot AGI con monitoreo en tiempo real, optimización automática y mantenimiento preventivo.`,
                        `El sistema está bajo gestión profesional con análisis continuo de rendimiento, seguridad y eficiencia operacional.`,
                        `Ejecutando gestión integral del sistema con capacidades de autodiagnóstico, optimización y mantenimiento automatizado.`
                    ],
                    professional_comprehensive: [
                        `Como su asistente de IA profesional integral, ofrezco servicios completos que incluyen análisis de negocios, gestión de sistemas, reconocimiento facial, procesamiento de emociones y mucho más.`,
                        `Mi sistema neural-simbólico completo está diseñado para proporcionar asistencia profesional de nivel empresarial en todos los módulos del sistema Bad Robot AGI.`,
                        `Ofrezco servicios profesionales integrales que combinan IA avanzada, reconocimiento facial, análisis emocional, gestión de archivos y capacidades de sistema completo.`
                    ]
                };

                return templates[responseType] || templates.help;
            }

            async performComprehensiveProfessionalWebSearch(query) {
                try {
                    console.log(`🔍 Performing comprehensive professional web search for: "${query}"`);

                    // Comprehensive professional web search implementation
                    const searchResults = await this.executeComprehensiveProfessionalWebSearch(query);

                    // Add search results to knowledge
                    this.addToKnowledge(`Comprehensive professional web search performed for: "${query}"`);
                    this.addToKnowledge(`Search results: ${searchResults.length} comprehensive professional items found`);

                    // Update rewards
                    this.rewards.taskCompletion += 20;
                    this.rewards.knowledgeGrowth += 15;
                    this.rewards.systemEfficiency += 12;

                    return `🔍 **Resultados de Búsqueda Profesional Integral para "${query}":**\n\n${searchResults.join('\n\n')}`;

                } catch (error) {
                    console.log(`❌ Comprehensive professional web search error: ${error.message}`);
                    return `Encontré un error al realizar la búsqueda web profesional integral. Permíteme utilizar las capacidades del sistema completo para ayudarte de otra manera.`;
                }
            }

            async executeComprehensiveProfessionalWebSearch(query) {
                // Comprehensive professional web search implementation
                const results = [
                    `📊 **Análisis Profesional Integral 1:** Información completa sobre "${query}" con análisis de múltiples fuentes autorizadas y verificadas.`,
                    `📈 **Investigación Empresarial 2:** Datos actualizados, estadísticas relevantes y tendencias del mercado relacionadas con "${query}".`,
                    `🔬 **Estudio Técnico Avanzado 3:** Hallazgos de investigación científica, opiniones de expertos y análisis técnico sobre "${query}".`,
                    `💼 **Aplicación Empresarial Integral 4:** Casos de uso profesionales, mejores prácticas y ejemplos de implementación de "${query}".`,
                    `🌐 **Perspectiva Global Profesional 5:** Análisis de mercado internacional, discusiones de la comunidad profesional y experiencias globales con "${query}".`,
                    `🎯 **Recomendaciones Estratégicas 6:** Sugerencias profesionales, estrategias de implementación y roadmap para "${query}".`
                ];

                // Simulate comprehensive professional search delay
                await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

                return results.slice(0, Math.floor(Math.random() * 4) + 3);
            }

            // Additional methods for complete system (abbreviated for space)
            calculateReward(message, response, action) {
                // Enhanced reward calculation for complete system
                const rewards = {
                    userSatisfaction: 10,
                    taskCompletion: 15,
                    learningProgress: 8,
                    systemEfficiency: 12,
                    knowledgeGrowth: 6
                };

                return { total: Object.values(rewards).reduce((sum, reward) => sum + reward, 0), breakdown: rewards };
            }

            updateRewards(rewardData) {
                Object.keys(rewardData.breakdown).forEach(key => {
                    this.rewards[key] += rewardData.breakdown[key];
                    this.rewards[key] = Math.min(this.rewards[key], 100);
                });
            }

            storeExperience(state, action, reward, nextState) {
                this.memory.push({ state, action, reward: reward.total, nextState, timestamp: Date.now() });
                if (this.memory.length > this.memorySize) this.memory.shift();
            }

            async trainDQN() {
                // Professional DQN training for complete system
                if (!this.qNetwork || this.memory.length < this.batchSize) return;
                console.log('🎯 Professional comprehensive DQN training in progress...');
            }

            updateTargetNetwork() {
                if (this.qNetwork && this.targetNetwork) {
                    const weights = this.qNetwork.getWeights();
                    this.targetNetwork.setWeights(weights);
                }
            }

            addToKnowledge(fact) {
                if (!this.facts.includes(fact)) {
                    this.facts.push(fact);
                    this.rewards.knowledgeGrowth += 4;
                }
            }

            updateAgentStatus() {
                const avgReward = Object.values(this.rewards).reduce((sum, reward) => sum + reward, 0) / 5;
                const agentIQ = Math.floor(130 + (avgReward * 1.0)); // Enhanced baseline IQ for complete system
                console.log(`🧠 Professional Complete System Agent Status - IQ: ${agentIQ}, Steps: ${this.stepCount}, Knowledge: ${this.facts.length}`);
            }

            startAutonomousMode() {
                this.isAutonomous = true;
                this.autonomousInterval = setInterval(() => this.performComprehensiveAutonomousActions(), 60000);
                console.log('🤖 Professional comprehensive autonomous mode activated');
            }

            stopAutonomousMode() {
                this.isAutonomous = false;
                if (this.autonomousInterval) clearInterval(this.autonomousInterval);
                console.log('🤖 Professional comprehensive autonomous mode deactivated');
            }

            performComprehensiveAutonomousActions() {
                console.log('🤖 Performing comprehensive professional autonomous learning actions');
                this.addToKnowledge('Autonomous learning cycle completed in complete system');
            }

            stop() {
                this.isActive = false;
                this.stopAutonomousMode();
                this.saveKnowledge();
                console.log('⏹️ Professional Complete System Neural-Symbolic Agent stopped');
            }
        }

        // Initialize Knowledge Base for Complete System
        function initializeKnowledgeBase() {
            knowledgeBase = {
                facts: [],
                rules: [],
                patterns: [],
                userProfiles: {},
                conversationHistory: [],
                learningMetrics: {
                    totalInteractions: 0,
                    successfulTasks: 0,
                    userSatisfactionScore: 0,
                    knowledgeGrowthRate: 0,
                    professionalQueries: 0,
                    systemOperations: 0
                }
            };

            const savedKnowledge = localStorage.getItem('badRobotCompleteProfessionalKnowledgeBase');
            if (savedKnowledge) {
                try {
                    const parsed = JSON.parse(savedKnowledge);
                    knowledgeBase = { ...knowledgeBase, ...parsed };
                    console.log(`📚 Loaded complete professional knowledge base with ${knowledgeBase.facts.length} facts`);
                } catch (error) {
                    console.log(`❌ Error loading complete professional knowledge base: ${error.message}`);
                }
            }
        }

        // Start Background Learning for Complete System
        function startBackgroundLearning() {
            setInterval(() => {
                if (neuralSymbolicAgent && neuralSymbolicAgent.isActive) {
                    console.log('🧠 Complete system background learning cycle');
                }
            }, 120000); // Every 2 minutes for complete system

            console.log('🧠 Professional complete system background learning processes started');
        }

        // Setup Agent Event Listeners for Complete System
        function setupAgentEventListeners() {
            console.log('🎛️ Setting up professional neural-symbolic agent event listeners for complete system');
        }

        // Enhanced processMessage function for Complete System
        async function processMessageWithCompleteProfessionalAgent(message) {
            try {
                const userContext = currentUser ? {
                    name: currentUser.name,
                    role: currentUser.role || 'User',
                    interactions: currentUser.interactions || 0,
                    lastSeen: currentUser.lastSeen,
                    preferences: currentUser.preferences || {},
                    emotions: currentUser.emotions || {}
                } : null;

                let response = '';

                if (neuralSymbolicAgent && neuralSymbolicAgent.isActive) {
                    response = await neuralSymbolicAgent.processMessage(message, userContext);
                } else {
                    response = await generatePersonalizedAIResponse(message, userContext);
                }

                addMessage('assistant', response, new Date().toISOString());

                if (currentUser) currentUser.interactions++;

            } catch (error) {
                console.error('Error processing message with complete professional agent:', error);
                addMessage('assistant', '❌ Lo siento, encontré un error procesando su solicitud en el sistema completo.', new Date().toISOString());
            }
        }

        // Initialize Professional Neural-Symbolic Agent for Complete System on page load
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initializing Professional Bad Robot AGI Complete System...');

            // Wait for other systems to initialize
            setTimeout(async () => {
                // Initialize professional neural-symbolic agent
                await initializeNeuralSymbolicAgent();

                // Initialize Professional Algorithms System integration
                await initializeProfessionalAlgorithmsIntegration();

                // Initialize Professional Trading Agent integration
                await initializeProfessionalTradingIntegrationComplete();

                // Initialize AI-Powered Search System integration
                await initializeAIPoweredSearchIntegrationComplete();

                // Start professional autonomous mode by default
                if (neuralSymbolicAgent && neuralSymbolicAgent.isActive) {
                    setTimeout(() => {
                        neuralSymbolicAgent.startAutonomousMode();
                    }, 5000); // Start autonomous mode after 5 seconds
                }

                console.log('✅ Professional Bad Robot AGI Complete System initialized successfully');
            }, 2000);
        });

        // ================ PROFESSIONAL ALGORITHMS SYSTEM INTEGRATION ================

        // Professional Algorithms System Integration for Complete System
        let professionalAlgorithmsSystem = null;
        let algorithmsSystemConnected = false;

        async function initializeProfessionalAlgorithmsIntegration() {
            console.log('🔐 Initializing Professional Algorithms System integration for complete system...');

            try {
                // Test connection to algorithms system
                const response = await fetch('http://localhost:9001/api/status');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        algorithmsSystemConnected = true;
                        professionalAlgorithmsSystem = data.status;

                        console.log('✅ Professional Algorithms System connected to complete system');
                        showNotification('🔐 Professional Algorithms System integrated!', 'success');

                        // Add algorithms system capabilities to complete system
                        addAlgorithmsSystemCapabilitiesToCompleteSystem();

                        // Add algorithms system UI elements
                        addAlgorithmsSystemUI();

                    } else {
                        console.log('⚠️ Professional Algorithms System not responding properly');
                    }
                } else {
                    console.log('⚠️ Professional Algorithms System not available');
                }

            } catch (error) {
                console.log(`⚠️ Professional Algorithms System connection failed: ${error.message}`);
            }
        }

        function addAlgorithmsSystemCapabilitiesToCompleteSystem() {
            // Add algorithms system commands to complete system processing
            console.log('🔧 Adding Professional Algorithms System capabilities to complete system');

            // The algorithms system will be integrated with all complete system components
        }

        function addAlgorithmsSystemUI() {
            // Add algorithms system UI elements to the complete interface
            try {
                // Add algorithms system button to the interface
                const algorithmButton = document.createElement('button');
                algorithmButton.innerHTML = '🔐 Algorithms System';
                algorithmButton.className = 'btn btn-primary';
                algorithmButton.style.margin = '5px';
                algorithmButton.onclick = () => {
                    window.open('http://localhost:9001', '_blank');
                };

                // Find a suitable container and add the button
                const container = document.querySelector('.container') || document.body;
                if (container) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.style.textAlign = 'center';
                    buttonContainer.style.margin = '10px';
                    buttonContainer.appendChild(algorithmButton);
                    container.insertBefore(buttonContainer, container.firstChild);
                }

                console.log('🔧 Algorithms system UI elements added');

            } catch (error) {
                console.log(`⚠️ Error adding algorithms system UI: ${error.message}`);
            }
        }

        // Professional Algorithms System Chat Commands for Complete System
        async function processAlgorithmsCommandComplete(message) {
            if (!algorithmsSystemConnected) {
                return "❌ Professional Algorithms System not available. Please ensure the system is running on port 9001.";
            }

            const lowerMessage = message.toLowerCase();

            try {
                // Enhanced encryption commands for complete system
                if (lowerMessage.includes('encrypt') || lowerMessage.includes('cifrar')) {
                    return await handleEncryptionCommandComplete(message);
                }

                // Enhanced hashing commands for complete system
                if (lowerMessage.includes('hash') || lowerMessage.includes('hash')) {
                    return await handleHashingCommandComplete(message);
                }

                // Code integration commands for complete system
                if (lowerMessage.includes('integrate code') || lowerMessage.includes('integrar código')) {
                    return await handleCodeIntegrationCommandComplete(message);
                }

                // Performance optimization commands
                if (lowerMessage.includes('optimize algorithms') || lowerMessage.includes('optimizar algoritmos')) {
                    return await handleOptimizeAlgorithmsCommand();
                }

                // Algorithms system status for complete system
                if (lowerMessage.includes('algorithms status') || lowerMessage.includes('estado algoritmos')) {
                    return await handleAlgorithmsStatusCommandComplete();
                }

                // Algorithms system help for complete system
                if (lowerMessage.includes('algorithms help') || lowerMessage.includes('ayuda algoritmos')) {
                    return getAlgorithmsHelpMessageComplete();
                }

                return null; // Not an algorithms command

            } catch (error) {
                console.error('❌ Algorithms command processing error:', error);
                return `❌ Error processing algorithms command: ${error.message}`;
            }
        }

        async function handleEncryptionCommandComplete(message) {
            try {
                // Enhanced encryption handling for complete system
                const textMatch = message.match(/encrypt[:\s]+"([^"]+)"/i) ||
                                 message.match(/cifrar[:\s]+"([^"]+)"/i);

                if (!textMatch) {
                    return '🔐 **Professional Encryption (Complete System):**\n\n' +
                           'Use: `encrypt: "your text here"`\n\n' +
                           'Available algorithms:\n' +
                           '• AES-256-GCM (Default)\n' +
                           '• ChaCha20-Poly1305\n' +
                           '• Fernet Professional\n' +
                           '• Custom XOR Advanced\n\n' +
                           'Examples:\n' +
                           '• `encrypt: "Hello World"`\n' +
                           '• `encrypt chacha: "Secret Message"`\n' +
                           '• `encrypt fernet: "Confidential Data"`';
                }

                const textToEncrypt = textMatch[1];
                let algorithm = 'AES_256_GCM';

                if (message.toLowerCase().includes('chacha')) {
                    algorithm = 'ChaCha20_Poly1305';
                } else if (message.toLowerCase().includes('fernet')) {
                    algorithm = 'Fernet_Professional';
                } else if (message.toLowerCase().includes('xor')) {
                    algorithm = 'Custom_XOR_Advanced';
                }

                const response = await fetch('http://localhost:9001/api/encrypt', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        algorithm: algorithm,
                        data: textToEncrypt
                    })
                });

                const result = await response.json();

                if (result.success) {
                    return `🔐 **Professional Encryption Result (Complete System):**\n\n` +
                           `**Algorithm:** ${algorithm}\n` +
                           `**Original Text:** ${textToEncrypt}\n` +
                           `**Encrypted:** ${result.ciphertext.substring(0, 80)}...\n` +
                           `**Key:** ${result.key ? result.key.substring(0, 40) + '...' : 'Generated'}\n` +
                           `**Execution Time:** ${result.execution_time.toFixed(4)}s\n` +
                           `**Status:** ✅ Success\n\n` +
                           `**Complete System Integration:** ✅ Active\n` +
                           `**Security Level:** 🔒 Professional Grade`;
                } else {
                    return `❌ **Encryption Failed:** ${result.error}`;
                }

            } catch (error) {
                return `❌ **Encryption Error:** ${error.message}`;
            }
        }

        async function handleOptimizeAlgorithmsCommand() {
            try {
                const response = await fetch('http://localhost:9001/api/optimize', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success) {
                    return `⚡ **Professional Algorithms Optimization Complete:**\n\n` +
                           `**Status:** ✅ Success\n` +
                           `**System:** Complete Bad Robot AGI Integration\n` +
                           `**Optimization Level:** Professional Grade\n` +
                           `**Performance:** Enhanced\n` +
                           `**Autonomous Learning:** Active\n\n` +
                           `**Message:** ${result.message}`;
                } else {
                    return `❌ **Optimization Failed:** ${result.error}`;
                }

            } catch (error) {
                return `❌ **Optimization Error:** ${error.message}`;
            }
        }

        function getAlgorithmsHelpMessageComplete() {
            return `🔐 **Professional Algorithms System (Complete System Integration):**\n\n` +
                   `**Available Commands:**\n` +
                   `• \`encrypt: "text"\` - AES-256-GCM encryption\n` +
                   `• \`encrypt chacha: "text"\` - ChaCha20-Poly1305\n` +
                   `• \`encrypt fernet: "text"\` - Fernet Professional\n` +
                   `• \`encrypt xor: "text"\` - Custom XOR Advanced\n` +
                   `• \`hash: "text"\` - SHA3-512 hashing\n` +
                   `• \`hash blake: "text"\` - BLAKE2b hashing\n` +
                   `• \`optimize algorithms\` - Performance optimization\n` +
                   `• \`algorithms status\` - Complete system status\n` +
                   `• \`algorithms help\` - This help message\n\n` +
                   `**Complete System Features:**\n` +
                   `• 🔐 Professional-grade cryptography\n` +
                   `• 🧠 Neural-symbolic integration\n` +
                   `• 🤖 Autonomous operation\n` +
                   `• 🌐 Internet connectivity\n` +
                   `• 🔧 Code integration analysis\n` +
                   `• ⚡ Performance optimization\n` +
                   `• 📊 Real-time monitoring\n\n` +
                   `**Web Interface:**\n` +
                   `🌐 http://localhost:9001 - Full algorithms interface\n` +
                   `🤖 Integrated with Bad Robot AGI Complete System`;
        }

        // ================ PROFESSIONAL TRADING AGENT INTEGRATION (COMPLETE SYSTEM) ================

        // Professional Trading Agent Integration for Complete System
        let professionalTradingAgentComplete = null;
        let tradingAgentConnectedComplete = false;

        async function initializeProfessionalTradingIntegrationComplete() {
            console.log('📈 Initializing Professional Trading Agent integration for complete system...');

            try {
                // Test connection to trading agent
                const response = await fetch('http://localhost:9002/api/status');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        tradingAgentConnectedComplete = true;
                        professionalTradingAgentComplete = data.status;

                        console.log('✅ Professional Trading Agent connected to complete system');
                        showNotification('📈 Professional Trading Agent integrated!', 'success');

                        // Add trading agent capabilities to complete system
                        addTradingAgentCapabilitiesToCompleteSystem();

                        // Add trading agent UI elements
                        addTradingAgentUI();

                    } else {
                        console.log('⚠️ Professional Trading Agent not responding properly');
                    }
                } else {
                    console.log('⚠️ Professional Trading Agent not available');
                }

            } catch (error) {
                console.log(`⚠️ Professional Trading Agent connection failed: ${error.message}`);
            }
        }

        function addTradingAgentCapabilitiesToCompleteSystem() {
            // Add trading agent commands to complete system processing
            console.log('📊 Adding Professional Trading Agent capabilities to complete system');
        }

        function addTradingAgentUI() {
            // Add trading agent UI elements to the complete interface
            try {
                // Add trading agent button to the interface
                const tradingButton = document.createElement('button');
                tradingButton.innerHTML = '📈 Trading Agent';
                tradingButton.className = 'btn btn-success';
                tradingButton.style.margin = '5px';
                tradingButton.onclick = () => {
                    window.open('http://localhost:9002', '_blank');
                };

                // Find a suitable container and add the button
                const container = document.querySelector('.container') || document.body;
                if (container) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.style.textAlign = 'center';
                    buttonContainer.style.margin = '10px';
                    buttonContainer.appendChild(tradingButton);
                    container.insertBefore(buttonContainer, container.firstChild);
                }

                console.log('📊 Trading agent UI elements added');

            } catch (error) {
                console.log(`⚠️ Error adding trading agent UI: ${error.message}`);
            }
        }

        // Professional Trading Agent Chat Commands for Complete System
        async function processTradingCommandComplete(message) {
            if (!tradingAgentConnectedComplete) {
                return "❌ Professional Trading Agent not available. Please ensure the system is running on port 9002.";
            }

            const lowerMessage = message.toLowerCase();

            try {
                // Enhanced stock commands for complete system
                if (lowerMessage.includes('stock') || lowerMessage.includes('acción')) {
                    return await handleStockCommandComplete(message);
                }

                // Enhanced prediction commands for complete system
                if (lowerMessage.includes('predict') || lowerMessage.includes('predecir')) {
                    return await handlePredictionCommandComplete(message);
                }

                // Enhanced analysis commands for complete system
                if (lowerMessage.includes('analyze') || lowerMessage.includes('analizar')) {
                    return await handleAnalysisCommandComplete(message);
                }

                // Portfolio management commands
                if (lowerMessage.includes('portfolio') || lowerMessage.includes('cartera')) {
                    return await handlePortfolioCommandComplete();
                }

                // Market overview commands
                if (lowerMessage.includes('market overview') || lowerMessage.includes('resumen mercado')) {
                    return await handleMarketOverviewCommand();
                }

                // Trading signals commands
                if (lowerMessage.includes('trading signals') || lowerMessage.includes('señales trading')) {
                    return await handleTradingSignalsCommand();
                }

                // Trading help for complete system
                if (lowerMessage.includes('trading help') || lowerMessage.includes('ayuda trading')) {
                    return getTradingHelpMessageComplete();
                }

                return null; // Not a trading command

            } catch (error) {
                console.error('❌ Trading command processing error:', error);
                return `❌ Error processing trading command: ${error.message}`;
            }
        }

        async function handleStockCommandComplete(message) {
            try {
                // Enhanced stock handling for complete system
                const symbolMatch = message.match(/(?:stock|acción)[:\s]+([A-Z]{1,5})/i) ||
                                   message.match(/([A-Z]{2,5})\s+(?:stock|acción)/i);

                if (!symbolMatch) {
                    return '📈 **Complete System Stock Analysis:**\n\n' +
                           'Use: `stock: SYMBOL` for comprehensive analysis\n\n' +
                           'Available commands:\n' +
                           '• `stock: AAPL` - Complete stock information\n' +
                           '• `predict: GOOGL` - AI price prediction\n' +
                           '• `analyze: TSLA` - Technical analysis\n' +
                           '• `portfolio` - Portfolio overview\n' +
                           '• `market overview` - Market summary\n' +
                           '• `trading signals` - Current trading signals\n\n' +
                           'Complete System Features:\n' +
                           '• 🔮 AI-powered predictions\n' +
                           '• 📊 Advanced technical analysis\n' +
                           '• 💼 Portfolio management\n' +
                           '• 🤖 Autonomous trading signals\n' +
                           '• 🌐 Real-time market data';
                }

                const symbol = symbolMatch[1].toUpperCase();

                // Get comprehensive stock data
                const [stockResponse, predictionResponse] = await Promise.all([
                    fetch('http://localhost:9002/api/stocks'),
                    fetch('http://localhost:9002/api/predict', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ symbol: symbol, time_horizon: '1d' })
                    })
                ]);

                const stockData = await stockResponse.json();
                const predictionData = await predictionResponse.json();

                if (stockData.success && stockData.stocks[symbol]) {
                    const stock = stockData.stocks[symbol];
                    const changeSymbol = stock.price_change >= 0 ? '+' : '';
                    const changeColor = stock.price_change >= 0 ? '🟢' : '🔴';

                    let result = `📈 **Complete System Analysis for ${symbol}:**\n\n` +
                                `**Company:** ${stock.name}\n` +
                                `**Sector:** ${stock.sector}\n` +
                                `**Current Price:** $${stock.current_price.toFixed(2)}\n` +
                                `**Change:** ${changeColor} ${changeSymbol}$${stock.price_change.toFixed(2)} (${changeSymbol}${stock.price_change_percent.toFixed(2)}%)\n` +
                                `**Volume:** ${stock.volume.toLocaleString()}\n` +
                                `**Avg Volume:** ${stock.avg_volume.toLocaleString()}\n` +
                                `**Market Cap:** $${(stock.market_cap / 1e9).toFixed(2)}B\n` +
                                `**P/E Ratio:** ${stock.pe_ratio ? stock.pe_ratio.toFixed(2) : 'N/A'}\n` +
                                `**Dividend Yield:** ${stock.dividend_yield ? (stock.dividend_yield * 100).toFixed(2) + '%' : 'N/A'}`;

                    // Add prediction if available
                    if (predictionData.success && predictionData.prediction) {
                        const pred = predictionData.prediction;
                        const predChangeSymbol = pred.price_change >= 0 ? '+' : '';
                        const predTrendEmoji = pred.price_change >= 0 ? '📈' : '📉';

                        result += `\n\n🔮 **AI Prediction (Complete System):**\n` +
                                 `**Predicted Price:** $${pred.predicted_price.toFixed(2)}\n` +
                                 `**Expected Change:** ${predTrendEmoji} ${predChangeSymbol}$${pred.price_change.toFixed(2)} (${predChangeSymbol}${pred.price_change_percent.toFixed(2)}%)\n` +
                                 `**Confidence:** ${(pred.confidence_score * 100).toFixed(1)}%\n` +
                                 `**Model:** ${pred.model_used}`;
                    }

                    result += `\n\n**Complete System Integration:** ✅ Active\n` +
                             `**Real-time Updates:** 🔄 Enabled\n` +
                             `**AI Analysis:** 🤖 Professional Grade`;

                    return result;
                } else {
                    return `❌ **Stock Data Not Available:** Could not retrieve data for ${symbol}.`;
                }

            } catch (error) {
                return `❌ **Complete System Stock Error:** ${error.message}`;
            }
        }

        function getTradingHelpMessageComplete() {
            return `📈 **Professional Trading Agent (Complete System Integration):**\n\n` +
                   `**Available Commands:**\n` +
                   `• \`stock: SYMBOL\` - Comprehensive stock analysis\n` +
                   `• \`predict: SYMBOL\` - AI price prediction with multiple models\n` +
                   `• \`analyze: SYMBOL\` - Advanced technical analysis\n` +
                   `• \`portfolio\` - Complete portfolio management\n` +
                   `• \`market overview\` - Market summary and trends\n` +
                   `• \`trading signals\` - Current trading opportunities\n` +
                   `• \`trading help\` - This help message\n\n` +
                   `**Complete System Features:**\n` +
                   `• 📈 Real-time stock data and analysis\n` +
                   `• 🔮 AI-powered price predictions (3 models)\n` +
                   `• 📊 Advanced technical indicators\n` +
                   `• 🤖 Autonomous trading signals\n` +
                   `• 💼 Professional portfolio management\n` +
                   `• 🌐 Internet-connected market data\n` +
                   `• ⚡ Performance optimization\n` +
                   `• 📱 Cross-platform compatibility\n\n` +
                   `**Web Interface:**\n` +
                   `🌐 http://localhost:9002 - Full trading dashboard\n` +
                   `🤖 Integrated with Bad Robot AGI Complete System\n\n` +
                   `**Popular Symbols:**\n` +
                   `• Tech Giants: AAPL, GOOGL, MSFT, AMZN, TSLA, META, NVDA\n` +
                   `• Financial: JPM, BAC, WFC, GS, MS, V, MA\n` +
                   `• ETFs: SPY, QQQ, IWM, VTI, ARKK\n` +
                   `• Crypto: BTC-USD, ETH-USD\n\n` +
                   `**Disclaimer:** Educational purposes only. Consult financial advisors for investment decisions.`;
        }

        // ================ AI-POWERED SEARCH SYSTEM INTEGRATION (COMPLETE SYSTEM) ================

        // AI-Powered Search System Integration for Complete System
        let aiPoweredSearchSystemComplete = null;
        let searchSystemConnectedComplete = false;

        async function initializeAIPoweredSearchIntegrationComplete() {
            console.log('🔍 Initializing AI-Powered Search System integration for complete system...');

            try {
                // Test connection to search system
                const response = await fetch('http://localhost:9003/api/status');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        searchSystemConnectedComplete = true;
                        aiPoweredSearchSystemComplete = data.status;

                        console.log('✅ AI-Powered Search System connected to complete system');
                        showNotification('🔍 AI-Powered Search System integrated!', 'success');

                        // Add search system capabilities to complete system
                        addSearchSystemCapabilitiesToCompleteSystem();

                        // Add search system UI elements
                        addSearchSystemUI();

                    } else {
                        console.log('⚠️ AI-Powered Search System not responding properly');
                    }
                } else {
                    console.log('⚠️ AI-Powered Search System not available');
                }

            } catch (error) {
                console.log(`⚠️ AI-Powered Search System connection failed: ${error.message}`);
            }
        }

        function addSearchSystemCapabilitiesToCompleteSystem() {
            // Add search system commands to complete system processing
            console.log('🔍 Adding AI-Powered Search System capabilities to complete system');
        }

        function addSearchSystemUI() {
            // Add search system UI elements to the complete interface
            try {
                // Add search system button to the interface
                const searchButton = document.createElement('button');
                searchButton.innerHTML = '🔍 Search System';
                searchButton.className = 'btn btn-info';
                searchButton.style.margin = '5px';
                searchButton.onclick = () => {
                    window.open('http://localhost:9003', '_blank');
                };

                // Find a suitable container and add the button
                const container = document.querySelector('.container') || document.body;
                if (container) {
                    const buttonContainer = document.createElement('div');
                    buttonContainer.style.textAlign = 'center';
                    buttonContainer.style.margin = '10px';
                    buttonContainer.appendChild(searchButton);
                    container.insertBefore(buttonContainer, container.firstChild);
                }

                console.log('🔍 Search system UI elements added');

            } catch (error) {
                console.log(`⚠️ Error adding search system UI: ${error.message}`);
            }
        }

        // AI-Powered Search System Chat Commands for Complete System
        async function processSearchCommandComplete(message) {
            if (!searchSystemConnectedComplete) {
                return "❌ AI-Powered Search System not available. Please ensure the system is running on port 9003.";
            }

            const lowerMessage = message.toLowerCase();

            try {
                // Enhanced search commands for complete system
                if (lowerMessage.includes('search') || lowerMessage.includes('buscar')) {
                    return await handleSearchCommandComplete(message);
                }

                // Advanced search commands
                if (lowerMessage.includes('research') || lowerMessage.includes('investigar')) {
                    return await handleResearchCommandComplete(message);
                }

                // Multi-engine search commands
                if (lowerMessage.includes('comprehensive search') || lowerMessage.includes('búsqueda completa')) {
                    return await handleComprehensiveSearchCommand(message);
                }

                // Search analytics commands
                if (lowerMessage.includes('search analytics') || lowerMessage.includes('análisis búsqueda')) {
                    return await handleSearchAnalyticsCommand();
                }

                // Search help for complete system
                if (lowerMessage.includes('search help') || lowerMessage.includes('ayuda búsqueda')) {
                    return getSearchHelpMessageComplete();
                }

                return null; // Not a search command

            } catch (error) {
                console.error('❌ Search command processing error:', error);
                return `❌ Error processing search command: ${error.message}`;
            }
        }

        async function handleSearchCommandComplete(message) {
            try {
                // Enhanced search handling for complete system
                const queryMatch = message.match(/(?:search|buscar)[:\s]+"([^"]+)"/i) ||
                                  message.match(/(?:search|buscar)[:\s]+(.+)/i);

                if (!queryMatch) {
                    return '🔍 **Complete System AI-Powered Search:**\n\n' +
                           'Use: `search: "your comprehensive query"`\n\n' +
                           'Available search types:\n' +
                           '• `search: "topic"` - Multi-engine search\n' +
                           '• `research: "topic"` - Deep research mode\n' +
                           '• `comprehensive search: "topic"` - All engines + analysis\n' +
                           '• `search analytics` - System performance metrics\n\n' +
                           'Complete System Features:\n' +
                           '• 🤖 AI-powered result ranking\n' +
                           '• 🔍 Multi-engine aggregation\n' +
                           '• 📊 Advanced analytics\n' +
                           '• 🎯 Intelligent summarization\n' +
                           '• 🔄 Smart caching system\n' +
                           '• 📋 Research-grade results';
                }

                const query = queryMatch[1].trim();

                // Use all available engines for complete system
                const response = await fetch('http://localhost:9003/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        engines: ['duckduckgo', 'google', 'bing', 'wikipedia', 'reddit', 'news'],
                        max_results_per_engine: 5
                    })
                });

                const result = await response.json();

                if (result.success && result.search_result) {
                    const searchResult = result.search_result;
                    const summary = searchResult.search_summary || {};

                    let responseText = `🔍 **Complete System Search Results for "${query}":**\n\n`;

                    // Enhanced summary for complete system
                    if (summary.summary) {
                        responseText += `📋 **AI Summary:**\n${summary.summary}\n\n`;
                    }

                    // Comprehensive execution info
                    responseText += `⚡ **Complete System Analysis:**\n`;
                    responseText += `• Total Results: ${searchResult.total_results}\n`;
                    responseText += `• Execution Time: ${searchResult.execution_time.toFixed(2)}s\n`;
                    responseText += `• Engines Used: ${searchResult.engines_used.join(', ')}\n`;
                    responseText += `• Sources: ${summary.sources ? summary.sources.join(', ') : 'Multiple'}\n`;
                    responseText += `• Confidence: ${(summary.confidence * 100).toFixed(1)}%\n`;
                    if (searchResult.from_cache) {
                        responseText += `• 🔄 From Intelligent Cache\n`;
                    }
                    responseText += `\n`;

                    // Enhanced results display
                    const topResults = searchResult.processed_results.slice(0, 8);
                    if (topResults.length > 0) {
                        responseText += `🎯 **Top Results (AI-Ranked):**\n\n`;

                        topResults.forEach((result, index) => {
                            const confidence = (result.composite_score * 100).toFixed(1);
                            const typeEmoji = getResultTypeEmoji(result.type);

                            responseText += `**${index + 1}. ${typeEmoji} ${result.title}**\n`;
                            responseText += `🔗 ${result.url}\n`;
                            responseText += `📝 ${result.snippet.substring(0, 200)}${result.snippet.length > 200 ? '...' : ''}\n`;
                            responseText += `📊 ${result.source} | ${result.type} | Confidence: ${confidence}%\n\n`;
                        });
                    }

                    responseText += `🌐 **Complete Search Interface:** http://localhost:9003\n`;
                    responseText += `🤖 **Complete System Integration:** ✅ Active\n`;
                    responseText += `💡 **Advanced Features:** Research mode, analytics, and comprehensive analysis available`;

                    return responseText;
                } else {
                    return `❌ **Complete System Search Failed:** ${result.error || 'Could not perform comprehensive search.'}`;
                }

            } catch (error) {
                return `❌ **Complete System Search Error:** ${error.message}`;
            }
        }

        function getResultTypeEmoji(type) {
            const typeEmojis = {
                'instant_answer': '⚡',
                'encyclopedia': '📚',
                'news': '📰',
                'web_result': '🌐',
                'social_media': '🤖',
                'related_topic': '🔗'
            };
            return typeEmojis[type] || '📄';
        }

        function getSearchHelpMessageComplete() {
            return `🔍 **AI-Powered Search System (Complete System Integration):**\n\n` +
                   `**Available Commands:**\n` +
                   `• \`search: "query"\` - Multi-engine comprehensive search\n` +
                   `• \`research: "topic"\` - Deep research with analysis\n` +
                   `• \`comprehensive search: "topic"\` - All engines + AI ranking\n` +
                   `• \`google: "query"\` - Google-specific search\n` +
                   `• \`duckduckgo: "query"\` - Privacy-focused search\n` +
                   `• \`wikipedia: "topic"\` - Encyclopedia search\n` +
                   `• \`news: "topic"\` - Latest news search\n` +
                   `• \`reddit: "topic"\` - Community discussions\n` +
                   `• \`search analytics\` - Performance metrics\n` +
                   `• \`search help\` - This help message\n\n` +
                   `**Complete System Features:**\n` +
                   `• 🤖 Advanced AI result ranking\n` +
                   `• 🔍 Multi-engine aggregation (6 engines)\n` +
                   `• 📊 Comprehensive analytics and metrics\n` +
                   `• 🎯 Intelligent result summarization\n` +
                   `• 🔄 Smart caching with performance optimization\n` +
                   `• 📋 Research-grade result processing\n` +
                   `• ⚡ Sub-second response times\n` +
                   `• 🌐 Cross-platform compatibility\n\n` +
                   `**Search Engines Integrated:**\n` +
                   `• 🦆 DuckDuckGo - Privacy-focused search\n` +
                   `• 🔍 Google - Comprehensive web search\n` +
                   `• 🔎 Bing - Microsoft search engine\n` +
                   `• 📚 Wikipedia - Encyclopedia articles\n` +
                   `• 🤖 Reddit - Community discussions\n` +
                   `• 📰 News - Latest news articles\n\n` +
                   `**Web Interface:**\n` +
                   `🌐 http://localhost:9003 - Full search dashboard\n` +
                   `🤖 Integrated with Bad Robot AGI Complete System\n\n` +
                   `**Advanced Examples:**\n` +
                   `• \`comprehensive search: "quantum computing breakthroughs 2024"\`\n` +
                   `• \`research: "artificial intelligence ethics"\`\n` +
                   `• \`search: "climate change solutions renewable energy"\``;
        }

        // Integrate search commands with existing complete system message processing
        const originalProcessMessageComplete = processMessage;

        async function processMessage(message) {
            // Check if it's a search command first
            const searchResponse = await processSearchCommandComplete(message);

            if (searchResponse) {
                addMessage('assistant', searchResponse, new Date().toISOString());
                return;
            }

            // Check if it's a trading command
            const tradingResponse = await processTradingCommandComplete(message);

            if (tradingResponse) {
                addMessage('assistant', tradingResponse, new Date().toISOString());
                return;
            }

            // Check if it's an algorithms command
            const algorithmsResponse = await processAlgorithmsCommandComplete(message);

            if (algorithmsResponse) {
                addMessage('assistant', algorithmsResponse, new Date().toISOString());
                return;
            }

            // If not a specialized command, use original processing
            return originalProcessMessageComplete(message);
        }

    </script>
</body>
</html>
